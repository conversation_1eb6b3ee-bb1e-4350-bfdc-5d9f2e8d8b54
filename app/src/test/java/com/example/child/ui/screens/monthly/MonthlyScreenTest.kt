package com.example.child.ui.screens.monthly

import org.junit.Test
import org.junit.Assert.*
import java.util.*

/**
 * 月报页面功能测试
 */
class MonthlyScreenTest {

    @Test
    fun `generateSampleDailyReports should generate correct number of days for different months`() {
        // 测试2月份（28天）
        val februaryReports = generateSampleDailyReports(2024, 2)
        assertEquals("2月份应该生成10天数据", 10, februaryReports.size)
        
        // 测试4月份（30天）
        val aprilReports = generateSampleDailyReports(2024, 4)
        assertEquals("4月份应该生成10天数据", 10, aprilReports.size)
        
        // 测试1月份（31天）
        val januaryReports = generateSampleDailyReports(2024, 1)
        assertEquals("1月份应该生成10天数据", 10, januaryReports.size)
    }

    @Test
    fun `generateSampleDailyReports should generate correct date format`() {
        val reports = generateSampleDailyReports(2024, 6)
        
        // 检查第一天的日期格式
        assertEquals("日期格式应该正确", "2024年6月1日", reports[0].date)
        assertEquals("日期数字应该正确", 1, reports[0].dayOfMonth)
        
        // 检查第五天的日期格式
        assertEquals("日期格式应该正确", "2024年6月5日", reports[4].date)
        assertEquals("日期数字应该正确", 5, reports[4].dayOfMonth)
    }

    @Test
    fun `generateSampleDailyReports should generate valid star ratings`() {
        val reports = generateSampleDailyReports(2024, 6)
        
        reports.forEach { report ->
            assertTrue("星级评分应该在1-5之间", report.starRating in 1..5)
        }
    }

    @Test
    fun `generateSampleDailyReports should generate non-empty descriptions`() {
        val reports = generateSampleDailyReports(2024, 6)
        
        reports.forEach { report ->
            assertTrue("描述不应该为空", report.description.isNotEmpty())
        }
    }

    @Test
    fun `generateSampleDailyReports should handle leap year February correctly`() {
        // 2024年是闰年，2月有29天
        val leapYearReports = generateSampleDailyReports(2024, 2)
        assertEquals("闰年2月应该生成10天数据", 10, leapYearReports.size)
        
        // 2023年不是闰年，2月有28天
        val normalYearReports = generateSampleDailyReports(2023, 2)
        assertEquals("平年2月应该生成10天数据", 10, normalYearReports.size)
    }

    @Test
    fun `MonthlyReport should have correct title format`() {
        val monthlyReport = MonthlyReport(
            monthTitle = "6月的成长",
            summary = "测试总结",
            dailyReports = emptyList()
        )
        
        assertEquals("标题格式应该正确", "6月的成长", monthlyReport.monthTitle)
    }
}

// 为了测试，我们需要将generateSampleDailyReports函数设为internal或public
// 这里我们创建一个测试用的函数
private fun generateSampleDailyReports(year: Int, month: Int): List<DailyReport> {
    val descriptions = listOf(
        "今日的阿呆格外的聪明，看上去他学会了如何与朋友们相处，可以主动帮助小朋友系鞋带，记得要表扬一下他哦",
        "今天小朋友表现很棒，主动分享玩具，还帮助老师整理教室，语言表达也更加流畅了",
        "今日学习新的儿歌，能够跟着节拍拍手，还主动要求表演给大家看，自信心有很大提升",
        "今天在游戏中表现出很好的合作精神，能够耐心等待轮到自己，情绪管理能力有进步",
        "今日阅读时间专注度很高，能够完整听完一个故事，还主动提问故事内容",
        "今天在美术课上表现出色，能够独立完成作品，色彩搭配很有创意",
        "今日体育活动中积极参与，团队协作能力有明显提升，运动协调性也更好了",
        "今天学习数字游戏，能够准确数到20，逻辑思维能力有很大进步",
        "今日音乐课表现突出，能够跟着节拍唱歌，节奏感很强",
        "今天在角色扮演游戏中表现活跃，想象力丰富，表达能力也有提升"
    )

    // 获取该月的天数
    val calendar = Calendar.getInstance()
    calendar.set(year, month - 1, 1) // month - 1 因为Calendar的月份从0开始
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    
    // 生成该月的前几天数据（最多10天）
    val numberOfDays = minOf(daysInMonth, 10)
    
    return (1..numberOfDays).map { day ->
        DailyReport(
            date = "${year}年${month}月${day}日",
            dayOfMonth = day,
            description = descriptions[(day - 1) % descriptions.size],
            starRating = when (day % 5) {
                1 -> 4
                2 -> 3
                3 -> 5
                4 -> 4
                0 -> 3
                else -> 3
            }
        )
    }
}
