package com.example.child.utils

import android.content.Context
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

data class UsbDeviceInfo(
    val deviceName: String,
    val totalSpace: Long,
    val freeSpace: Long,
    val mountPath: String?
)

@Singleton
class UsbDeviceManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    private val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
    
    private val _usbDevices = MutableStateFlow<List<UsbDeviceInfo>>(emptyList())
    val usbDevices: StateFlow<List<UsbDeviceInfo>> = _usbDevices.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()
    
    fun scanForUsbDevices() {
        _isScanning.value = true
        
        try {
            val devices = mutableListOf<UsbDeviceInfo>()
            
            // 获取USB设备
            val usbDevices = usbManager.deviceList
            for ((_, device) in usbDevices) {
                if (isStorageDevice(device)) {
                    val deviceInfo = createUsbDeviceInfo(device)
                    deviceInfo?.let { devices.add(it) }
                }
            }
            
            // 获取存储卷信息
            val storageVolumes = storageManager.storageVolumes
            for (volume in storageVolumes) {
                if (volume.isRemovable && volume.state == "mounted") {
                    val deviceInfo = createUsbDeviceInfoFromVolume(volume)
                    deviceInfo?.let { devices.add(it) }
                }
            }
            
            _usbDevices.value = devices
        } catch (e: Exception) {
            // Handle error
            _usbDevices.value = emptyList()
        } finally {
            _isScanning.value = false
        }
    }
    
    private fun isStorageDevice(device: UsbDevice): Boolean {
        // 简单的存储设备检测逻辑
        return device.deviceClass == android.hardware.usb.UsbConstants.USB_CLASS_MASS_STORAGE ||
                device.deviceName.contains("storage", ignoreCase = true) ||
                device.deviceName.contains("disk", ignoreCase = true)
    }
    
    private fun createUsbDeviceInfo(device: UsbDevice): UsbDeviceInfo? {
        return try {
            UsbDeviceInfo(
                deviceName = device.deviceName,
                totalSpace = 0L, // 需要更复杂的逻辑来获取实际大小
                freeSpace = 0L,
                mountPath = null
            )
        } catch (e: Exception) {
            null
        }
    }
    
    private fun createUsbDeviceInfoFromVolume(volume: StorageVolume): UsbDeviceInfo? {
        return try {
            val path = getVolumePath(volume)
            val file = File(path)
            
            UsbDeviceInfo(
                deviceName = volume.getDescription(context) ?: "USB设备",
                totalSpace = file.totalSpace,
                freeSpace = file.freeSpace,
                mountPath = path
            )
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getVolumePath(volume: StorageVolume): String {
        return try {
            // 使用反射获取路径（Android 11+需要特殊处理）
            val getPathMethod = volume.javaClass.getMethod("getPath")
            getPathMethod.invoke(volume) as String
        } catch (e: Exception) {
            "/storage/unknown"
        }
    }
    
    fun clearUsbDevice(deviceInfo: UsbDeviceInfo): Boolean {
        return try {
            deviceInfo.mountPath?.let { path ->
                val directory = File(path)
                if (directory.exists() && directory.isDirectory) {
                    deleteRecursively(directory)
                    true
                } else {
                    false
                }
            } ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    private fun deleteRecursively(file: File): Boolean {
        return try {
            if (file.isDirectory) {
                file.listFiles()?.forEach { child ->
                    deleteRecursively(child)
                }
            }
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    fun getAudioFiles(deviceInfo: UsbDeviceInfo): List<File> {
        val audioFiles = mutableListOf<File>()
        
        deviceInfo.mountPath?.let { path ->
            val directory = File(path)
            if (directory.exists() && directory.isDirectory) {
                findAudioFiles(directory, audioFiles)
            }
        }
        
        return audioFiles
    }
    
    private fun findAudioFiles(directory: File, audioFiles: MutableList<File>) {
        try {
            directory.listFiles()?.forEach { file ->
                if (file.isDirectory) {
                    findAudioFiles(file, audioFiles)
                } else if (isAudioFile(file)) {
                    audioFiles.add(file)
                }
            }
        } catch (e: Exception) {
            // Handle error
        }
    }
    
    private fun isAudioFile(file: File): Boolean {
        val audioExtensions = listOf("mp3", "wav", "m4a", "aac", "ogg", "flac")
        val extension = file.extension.lowercase()
        return audioExtensions.contains(extension)
    }
}
