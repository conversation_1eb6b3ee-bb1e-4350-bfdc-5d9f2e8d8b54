package com.example.child.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaPlayer
import android.util.Log
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 静态资源管理器
 * 用于管理和访问 assets 文件夹中的各种资源
 */
@Singleton
class AssetManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    /**
     * 图片资源路径常量
     */
    object ImagePaths {
        const val BACKGROUNDS = "images/backgrounds/"
        const val ICONS = "images/icons/"
        const val AVATARS = "images/avatars/"
        const val ILLUSTRATIONS = "images/illustrations/"
        const val CHARTS = "images/charts/"
    }

    /**
     * 音频资源路径常量
     */
    object AudioPaths {
        const val VOICE_SAMPLES = "audio/voice_samples/"
        const val BACKGROUND_MUSIC = "audio/background_music/"
        const val SOUND_EFFECTS = "audio/sound_effects/"
    }

    /**
     * 数据资源路径常量
     */
    object DataPaths {
        const val JSON = "data/json/"
        const val CSV = "data/csv/"
        const val XML = "data/xml/"
        const val CONFIG = "data/config/"
    }

    /**
     * 从 assets 加载图片为 Bitmap
     */
    suspend fun loadImage(fileName: String, folder: String = ImagePaths.BACKGROUNDS): Bitmap? {
        return withContext(Dispatchers.IO) {
            try {
                val fullPath = "$folder$fileName"
                Log.d("AssetManager", "尝试加载图片: $fullPath")
                val inputStream: InputStream = context.assets.open(fullPath)
                val bitmap = BitmapFactory.decodeStream(inputStream)
                Log.d("AssetManager", "图片加载${if (bitmap != null) "成功" else "失败"}: $fullPath")
                bitmap
            } catch (e: IOException) {
                Log.e("AssetManager", "加载图片失败: $folder$fileName", e)
                e.printStackTrace()
                null
            }
        }
    }

    /**
     * 从 assets 加载图片为 ImageBitmap (用于 Compose)
     */
    suspend fun loadImageBitmap(fileName: String, folder: String = ImagePaths.BACKGROUNDS): ImageBitmap? {
        return loadImage(fileName, folder)?.asImageBitmap()
    }

    /**
     * 从 assets 加载文本文件
     */
    suspend fun loadTextFile(fileName: String, folder: String = DataPaths.JSON): String? {
        return withContext(Dispatchers.IO) {
            try {
                val inputStream: InputStream = context.assets.open("$folder$fileName")
                inputStream.bufferedReader().use { it.readText() }
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }
    }

    /**
     * 从 assets 加载 JSON 文件并解析
     */
    suspend fun loadJsonFile(fileName: String): String? {
        return loadTextFile(fileName, DataPaths.JSON)
    }

    /**
     * 获取文件夹中的所有文件列表
     */
    suspend fun getFileList(folder: String): List<String> {
        return withContext(Dispatchers.IO) {
            try {
                context.assets.list(folder)?.toList() ?: emptyList()
            } catch (e: IOException) {
                e.printStackTrace()
                emptyList()
            }
        }
    }

    /**
     * 检查文件是否存在
     */
    suspend fun fileExists(fileName: String, folder: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                context.assets.open("$folder$fileName").use { true }
            } catch (e: IOException) {
                false
            }
        }
    }

    /**
     * 创建音频播放器
     */
    fun createAudioPlayer(fileName: String, folder: String = AudioPaths.VOICE_SAMPLES): MediaPlayer? {
        return try {
            val assetFileDescriptor = context.assets.openFd("$folder$fileName")
            MediaPlayer().apply {
                setDataSource(
                    assetFileDescriptor.fileDescriptor,
                    assetFileDescriptor.startOffset,
                    assetFileDescriptor.length
                )
                prepare()
            }
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 获取 InputStream
     */
    fun getInputStream(fileName: String, folder: String): InputStream? {
        return try {
            context.assets.open("$folder$fileName")
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 复制 assets 文件到内部存储
     */
    suspend fun copyAssetToInternalStorage(
        fileName: String,
        folder: String,
        destinationPath: String
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val inputStream = context.assets.open("$folder$fileName")
                val outputStream = context.openFileOutput(destinationPath, Context.MODE_PRIVATE)

                inputStream.use { input ->
                    outputStream.use { output ->
                        input.copyTo(output)
                    }
                }
                true
            } catch (e: IOException) {
                e.printStackTrace()
                false
            }
        }
    }
}

/**
 * 资源文件信息数据类
 */
data class AssetFileInfo(
    val fileName: String,
    val folder: String,
    val fullPath: String,
    val size: Long = 0L,
    val type: AssetType
)

/**
 * 资源类型枚举
 */
enum class AssetType {
    IMAGE,
    AUDIO,
    VIDEO,
    DOCUMENT,
    DATA,
    FONT,
    OTHER
}

/**
 * 扩展函数：根据文件扩展名判断资源类型
 */
fun String.getAssetType(): AssetType {
    return when (this.substringAfterLast('.', "").lowercase()) {
        "jpg", "jpeg", "png", "gif", "bmp", "webp" -> AssetType.IMAGE
        "mp3", "wav", "aac", "ogg", "m4a" -> AssetType.AUDIO
        "mp4", "avi", "mov", "mkv", "webm" -> AssetType.VIDEO
        "pdf", "doc", "docx", "txt", "rtf" -> AssetType.DOCUMENT
        "json", "xml", "csv", "yaml", "yml" -> AssetType.DATA
        "ttf", "otf", "woff", "woff2" -> AssetType.FONT
        else -> AssetType.OTHER
    }
}

/**
 * Compose 可组合函数：加载 assets 图片
 */
@Composable
fun rememberAssetImage(
    fileName: String,
    folder: String = AssetManager.ImagePaths.BACKGROUNDS,
    assetManager: AssetManager
): ImageBitmap? {
    var imageBitmap by remember(fileName, folder) { mutableStateOf<ImageBitmap?>(null) }

    LaunchedEffect(fileName, folder) {
        Log.d("rememberAssetImage", "开始加载图片: $folder$fileName")
        imageBitmap = assetManager.loadImageBitmap(fileName, folder)
        Log.d("rememberAssetImage", "图片加载完成: ${imageBitmap != null}")
    }

    return imageBitmap
}
