package com.example.child.ui.screens.splash

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class SplashUiState(
    val isLoading: Boolean = true,
    val navigationTarget: String? = null
)

@HiltViewModel
class SplashViewModel @Inject constructor(
    // TODO: 注入认证仓库和偏好设置管理器
) : ViewModel() {

    private val _uiState = MutableStateFlow(SplashUiState())
    val uiState: StateFlow<SplashUiState> = _uiState.asStateFlow()

    fun checkAuthStatus() {
        viewModelScope.launch {
            try {
                // TODO: 检查用户登录状态
                // 1. 检查是否是首次启动
                // 2. 检查用户是否已登录
                // 3. 检查token是否有效

                // 模拟检查过程
                kotlinx.coroutines.delay(500)

                // 暂时直接导航到登录页
                val navigationTarget = "login"

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    navigationTarget = navigationTarget
                )
            } catch (e: Exception) {
                // 出错时导航到登录页
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    navigationTarget = "login"
                )
            }
        }
    }
}
