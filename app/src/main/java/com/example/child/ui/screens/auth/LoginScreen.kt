package com.example.child.ui.screens.auth

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.example.child.ui.theme.*

@Composable
fun LoginScreen(
    navController: NavController,
    viewModel: LoginViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
        ) {
            Spacer(modifier = Modifier.height(80.dp))

            // 登录类型切换标题
            Text(
                text = if (uiState.loginType == LoginType.SMS) "短信登录" else "密码登录",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1A1A1A)
            )

            Spacer(modifier = Modifier.height(32.dp))

            // 手机号输入
            OutlinedTextField(
                value = uiState.phone,
                onValueChange = viewModel::updatePhone,
                placeholder = {
                    Text(
                        "请输入手机号",
                        color = Color(0xFF999999),
                        fontSize = 16.sp
                    )
                },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(0.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color.Transparent,
                    unfocusedBorderColor = Color.Transparent,
                ),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                isError = uiState.phoneError.isNotEmpty()
            )

            // 手机号下划线
            Divider(
                color = if (uiState.phoneError.isNotEmpty()) Color.Red else Color(0xFFEEEEEE),
                thickness = 1.dp
            )

            if (uiState.phoneError.isNotEmpty()) {
                Text(
                    text = uiState.phoneError,
                    color = Color.Red,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 根据登录类型显示不同的输入框
            when (uiState.loginType) {
                LoginType.SMS -> {
                    // 验证码输入
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = uiState.verificationCode,
                            onValueChange = viewModel::updateVerificationCode,
                            placeholder = {
                                Text(
                                    "请输入验证码",
                                    color = Color(0xFF999999),
                                    fontSize = 16.sp
                                )
                            },
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(0.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedContainerColor = Color.Transparent,
                                unfocusedContainerColor = Color.Transparent,
                                focusedBorderColor = Color.Transparent,
                                unfocusedBorderColor = Color.Transparent,
                            ),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            isError = uiState.codeError.isNotEmpty()
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        // 发送验证码按钮
                        TextButton(
                            onClick = viewModel::sendVerificationCode,
                            enabled = uiState.countdown == 0 && !uiState.isSendingCode
                        ) {
                            when {
                                uiState.isSendingCode -> {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(16.dp),
                                        color = Primary,
                                        strokeWidth = 2.dp
                                    )
                                }
                                uiState.countdown > 0 -> {
                                    Text(
                                        text = "${uiState.countdown}s",
                                        color = Color(0xFF999999),
                                        fontSize = 14.sp
                                    )
                                }
                                else -> {
                                    Text(
                                        text = "发送验证码",
                                        color = Primary,
                                        fontSize = 14.sp
                                    )
                                }
                            }
                        }
                    }

                    // 验证码下划线
                    Divider(
                        color = if (uiState.codeError.isNotEmpty()) Color.Red else Color(0xFFEEEEEE),
                        thickness = 1.dp
                    )

                    if (uiState.codeError.isNotEmpty()) {
                        Text(
                            text = uiState.codeError,
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                LoginType.PASSWORD -> {
                    // 密码输入
                    var passwordVisible by remember { mutableStateOf(false) }

                    OutlinedTextField(
                        value = uiState.password,
                        onValueChange = viewModel::updatePassword,
                        placeholder = {
                            Text(
                                "请输入密码",
                                color = Color(0xFF999999),
                                fontSize = 16.sp
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(0.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedContainerColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent,
                            focusedBorderColor = Color.Transparent,
                            unfocusedBorderColor = Color.Transparent,
                        ),
                        visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                        trailingIcon = {
                            IconButton(onClick = { passwordVisible = !passwordVisible }) {
                                Icon(
                                    imageVector = if (passwordVisible) Icons.Default.Lock else Icons.Default.Lock,
                                    contentDescription = if (passwordVisible) "隐藏密码" else "显示密码",
                                    tint = Color(0xFF999999)
                                )
                            }
                        },
                        isError = uiState.passwordError.isNotEmpty()
                    )

                    // 密码下划线
                    Divider(
                        color = if (uiState.passwordError.isNotEmpty()) Color.Red else Color(0xFFEEEEEE),
                        thickness = 1.dp
                    )

                    if (uiState.passwordError.isNotEmpty()) {
                        Text(
                            text = uiState.passwordError,
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(40.dp))

            // 登录按钮
            Button(
                onClick = viewModel::login,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                enabled = !uiState.isLoading && when (uiState.loginType) {
                    LoginType.SMS -> uiState.phone.isNotEmpty() && uiState.verificationCode.isNotEmpty()
                    LoginType.PASSWORD -> uiState.phone.isNotEmpty() && uiState.password.isNotEmpty()
                },
                shape = RoundedCornerShape(12.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Primary,
                    contentColor = Color.White
                )
            ) {
                if (uiState.isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(
                        text = "登录",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 底部切换和忘记密码
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 登录方式切换
                Text(
                    text = if (uiState.loginType == LoginType.SMS) "密码登录" else "短信登录",
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    modifier = Modifier.clickable {
                        val newType = if (uiState.loginType == LoginType.SMS) LoginType.PASSWORD else LoginType.SMS
                        viewModel.switchLoginType(newType)
                    }
                )

                // 忘记密码（只在密码登录时显示）
                if (uiState.loginType == LoginType.PASSWORD) {
                    Text(
                        text = "忘记密码?",
                        color = Color(0xFFFFC107),
                        fontSize = 14.sp,
                        modifier = Modifier.clickable {
                            navController.navigate("forgot_password")
                        }
                    )
                }
            }
        }
    }

    // 处理导航
    uiState.navigationTarget?.let { target ->
        LaunchedEffect(target) {
            navController.navigate(target) {
                popUpTo("login") { inclusive = true }
            }
        }
    }

    // 显示错误信息
    uiState.error.takeIf { it.isNotEmpty() }?.let { error ->
        LaunchedEffect(error) {
            // TODO: 显示错误提示
        }
    }
}
