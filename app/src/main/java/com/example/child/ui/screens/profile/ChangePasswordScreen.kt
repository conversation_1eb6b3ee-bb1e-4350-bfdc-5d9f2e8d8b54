package com.example.child.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.R
import com.example.child.ui.components.*
import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChangePasswordScreen(
    onBackClick: () -> Unit = {},
    onLogout: () -> Unit,
    viewModel: ChangePasswordViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // 渐变背景 - 与ProfileScreen保持一致
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        0f to Color(0xFFFFE389),
                        0.3f to Color(0xFFF0F0F0),
                    )
                )
        )

        // 可滚动内容
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(60.dp))

                // 顶部标题栏
                TopAppBar(
                    title = "修改密码",
                    onBackClick = onBackClick
                )
            }

            item {
                Spacer(modifier = Modifier.height(32.dp))

                // 密码修改表单
                PasswordChangeForm(
                    oldPassword = uiState.oldPassword,
                    onOldPasswordChange = viewModel::updateOldPassword,
                    newPassword = uiState.newPassword,
                    onNewPasswordChange = viewModel::updateNewPassword,
                    confirmPassword = uiState.confirmPassword,
                    onConfirmPasswordChange = viewModel::updateConfirmPassword
                )
            }

            item {
                Spacer(modifier = Modifier.height(32.dp))

                // 保存按钮
                SaveButton(
                    isLoading = uiState.isLoading,
                    onClick = {
                        viewModel.changePassword()
                    }
                )
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))

                // 退出登录按钮
                LogoutButton(
                    onLogout = onLogout
                )
            }

            item {
                Spacer(modifier = Modifier.height(100.dp))
            }
        }
    }

    // 显示错误信息
    uiState.error.takeIf { it.isNotEmpty() }?.let { error ->
        LaunchedEffect(error) {
            // TODO: 显示错误提示
        }
    }

    // 修改成功提示
    if (uiState.changeSuccess) {
        LaunchedEffect(uiState.changeSuccess) {
            // TODO: 显示成功提示并返回
            onBackClick()
        }
    }
}

@Composable
private fun TopAppBar(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
    }
}

@Composable
private fun PasswordChangeForm(
    oldPassword: String,
    onOldPasswordChange: (String) -> Unit,
    newPassword: String,
    onNewPasswordChange: (String) -> Unit,
    confirmPassword: String,
    onConfirmPasswordChange: (String) -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 原密码
            PasswordInputField(
                label = stringResource(id = R.string.old_password),
                value = oldPassword,
                onValueChange = onOldPasswordChange,
                placeholder = "输入原密码"
            )

            Divider(color = Divider)

            // 新密码
            PasswordInputField(
                label = stringResource(id = R.string.new_password),
                value = newPassword,
                onValueChange = onNewPasswordChange,
                placeholder = "输入新密码"
            )

            Divider(color = Divider)

            // 确认密码
            PasswordInputField(
                label = stringResource(id = R.string.confirm_password),
                value = confirmPassword,
                onValueChange = onConfirmPasswordChange,
                placeholder = "输入新密码"
            )
        }
    }
}

@Composable
private fun PasswordInputField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String
) {
    var passwordVisible by remember { mutableStateOf(false) }

    Column {
        Text(
            text = label,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = TextPrimary,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        OutlinedTextField(
            value = value,
            onValueChange = onValueChange,
            placeholder = {
                Text(
                    text = placeholder,
                    color = TextSecondary,
                    fontSize = 14.sp
                )
            },
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Lock else Icons.Default.Lock,
                        contentDescription = if (passwordVisible) "隐藏密码" else "显示密码",
                        tint = TextSecondary
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Primary,
                unfocusedBorderColor = Divider,
                focusedTextColor = TextPrimary,
                unfocusedTextColor = TextPrimary
            ),
            shape = RoundedCornerShape(8.dp)
        )
    }
}

@Composable
private fun SaveButton(
    isLoading: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = !isLoading,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Primary,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
        } else {
            Text(
                text = "修改密码",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun LogoutButton(
    onLogout: () -> Unit
) {
    var showLogoutDialog by remember { mutableStateOf(false) }

    Button(
        onClick = { showLogoutDialog = true },
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.error,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Icon(
            imageVector = Icons.Default.ExitToApp,
            contentDescription = null,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = stringResource(id = R.string.logout),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }

    // 退出登录确认对话框
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = {
                Text(text = stringResource(id = R.string.logout_confirm_title))
            },
            text = {
                Text(text = stringResource(id = R.string.logout_confirm_message))
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        onLogout()
                        showLogoutDialog = false
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.confirm),
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLogoutDialog = false }
                ) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }
}