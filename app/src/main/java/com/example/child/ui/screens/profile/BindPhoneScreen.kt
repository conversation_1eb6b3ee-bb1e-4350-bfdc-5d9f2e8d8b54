package com.example.child.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BindPhoneScreen(
    onBackClick: () -> Unit = {},
    viewModel: BindPhoneViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "绑定手机",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Primary
                )
            )

            // 可滚动内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // 绑定手机表单
                    BindPhoneForm(
                        phone = uiState.phone,
                        onPhoneChange = viewModel::updatePhone,
                        verificationCode = uiState.verificationCode,
                        onVerificationCodeChange = viewModel::updateVerificationCode,
                        countdown = uiState.countdown,
                        isSendingCode = uiState.isSendingCode,
                        onSendCode = viewModel::sendVerificationCode,
                        phoneError = uiState.phoneError,
                        codeError = uiState.codeError
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(32.dp))

                    // 绑定按钮
                    BindButton(
                        isLoading = uiState.isLoading,
                        onClick = {
                            viewModel.bindPhone()
                        }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }
    }

    // 显示错误信息
    uiState.error.takeIf { it.isNotEmpty() }?.let { error ->
        LaunchedEffect(error) {
            // TODO: 显示错误提示
        }
    }

    // 绑定成功提示
    if (uiState.bindSuccess) {
        LaunchedEffect(uiState.bindSuccess) {
            // TODO: 显示成功提示并返回
            onBackClick()
        }
    }
}

@Composable
private fun BindPhoneForm(
    phone: String,
    onPhoneChange: (String) -> Unit,
    verificationCode: String,
    onVerificationCodeChange: (String) -> Unit,
    countdown: Int,
    isSendingCode: Boolean,
    onSendCode: () -> Unit,
    phoneError: String,
    codeError: String
) {
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // 手机号输入
            PhoneInputField(
                label = "手机号",
                value = phone,
                onValueChange = onPhoneChange,
                placeholder = "请输入手机号",
                error = phoneError
            )

            HorizontalDivider(color = Divider)

            // 验证码输入
            VerificationCodeInputField(
                label = "验证码",
                value = verificationCode,
                onValueChange = onVerificationCodeChange,
                placeholder = "请输入验证码",
                countdown = countdown,
                isSendingCode = isSendingCode,
                onSendCode = onSendCode,
                error = codeError
            )

            HorizontalDivider(color = Divider)
        }
    }
}

@Composable
private fun PhoneInputField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    error: String
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.height(60.dp),
        ) {
            Column(
                modifier = Modifier.width(80.dp),
            ) {
                Text(
                    text = label,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
            }

            Spacer(modifier = Modifier.width(10.dp))

            Column {
                OutlinedTextField(
                    value = value,
                    onValueChange = onValueChange,
                    placeholder = {
                        Text(
                            text = placeholder,
                            color = TextSecondary,
                            fontSize = 14.sp
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary
                    ),
                    shape = RoundedCornerShape(8.dp),
                    isError = error.isNotEmpty()
                )
            }
        }
        
        if (error.isNotEmpty()) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 90.dp, top = 4.dp)
            )
        }
    }
}

@Composable
private fun VerificationCodeInputField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    countdown: Int,
    isSendingCode: Boolean,
    onSendCode: () -> Unit,
    error: String
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.height(60.dp),
        ) {
            Column(
                modifier = Modifier.width(80.dp),
            ) {
                Text(
                    text = label,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
            }

            Spacer(modifier = Modifier.width(10.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = value,
                    onValueChange = onValueChange,
                    placeholder = {
                        Text(
                            text = placeholder,
                            color = TextSecondary,
                            fontSize = 14.sp
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.weight(1f),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary
                    ),
                    shape = RoundedCornerShape(8.dp),
                    isError = error.isNotEmpty()
                )

                Spacer(modifier = Modifier.width(12.dp))

                // 发送验证码按钮
                Button(
                    onClick = onSendCode,
                    enabled = countdown == 0 && !isSendingCode,
                    modifier = Modifier
                        .width(100.dp)
                        .height(40.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Primary,
                        contentColor = Color.White,
                        disabledContainerColor = Color.Gray,
                        disabledContentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    when {
                        isSendingCode -> {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                        }
                        countdown > 0 -> {
                            Text(
                                text = "${countdown}s",
                                fontSize = 12.sp
                            )
                        }
                        else -> {
                            Text(
                                text = "发送",
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }
        }
        
        if (error.isNotEmpty()) {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 90.dp, top = 4.dp)
            )
        }
    }
}

@Composable
private fun BindButton(
    isLoading: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = !isLoading,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Primary,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
        } else {
            Text(
                text = "绑定手机",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
