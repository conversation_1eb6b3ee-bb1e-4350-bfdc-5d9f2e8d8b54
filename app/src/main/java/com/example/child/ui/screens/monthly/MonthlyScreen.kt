package com.example.child.ui.screens.monthly

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.Image
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.ui.components.MonthYearPickerDialog
import com.example.child.ui.screens.home.HomeViewModel
import com.example.child.utils.AssetManager
import com.example.child.utils.rememberAssetImage
import java.text.SimpleDateFormat
import java.util.*

// 数据模型
data class DailyReport(
    val date: String,
    val dayOfMonth: Int,
    val description: String,
    val starRating: Int, // 1-5 小红花数量
    val isToday: Boolean = false
)

data class MonthlyReport(
    val monthTitle: String,
    val summary: String,
    val dailyReports: List<DailyReport>
)


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonthlyScreen(
    onNavigateToHome: (String) -> Unit = {},
    assetManager: AssetManager = hiltViewModel<MonthlyViewModel>().assetManager
) {
    var selectedYear by remember { mutableStateOf(2024) }
    var selectedMonth by remember { mutableStateOf(6) } // 5月
    var showMonthPicker by remember { mutableStateOf(false) }

    // 格式化显示的月份文本
    val displayMonth = "${selectedYear}年${selectedMonth}月"

    // 根据选择的月份动态生成数据
    val monthlyReport = remember(selectedYear, selectedMonth) {
        MonthlyReport(
            monthTitle = "${selectedMonth}月的成长",
            summary = "本月小朋友有了明显的提高，语言表达的流动性很多，小朋友能表达更多的内容，说话时间变长，互动也更加积极主动",
            dailyReports = generateSampleDailyReports(selectedYear, selectedMonth)
        )
    }

    // 加载背景图片
    val backgroundImage = rememberAssetImage(
        fileName = "month-bg.png",
        folder = AssetManager.ImagePaths.BACKGROUNDS,
        assetManager = assetManager
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF9FAFB))
    ) {
        if (backgroundImage != null) {
            Image(
                bitmap = backgroundImage,
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop
            )
        }
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(60.dp))

                // 标题和月份选择器
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = monthlyReport.monthTitle,
                        fontSize = 28.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )

                    Card(
                        modifier = Modifier.clickable { showMonthPicker = true },
                        colors = CardDefaults.cardColors(containerColor = Color.White),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = displayMonth,
                                color = Color(0xFF333333),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "选择月份",
                                tint = Color(0xFF666666),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }

            // 月度总结
            item {
                Text(
                    text = monthlyReport.summary,
                    color = Color.White,
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            }

            // 每日报告列表
            items(monthlyReport.dailyReports) { dailyReport ->
                DailyReportCard(
                    dailyReport = dailyReport,
                    onClick = {
                        // 跳转到首页并切换到对应日期
                        onNavigateToHome(dailyReport.date)
                    }
                )
            }

            item {
                Spacer(modifier = Modifier.height(100.dp))
            }
        }
    }

    // 月份选择器对话框
    if (showMonthPicker) {
        MonthYearPickerDialog(
            selectedYear = selectedYear,
            selectedMonth = selectedMonth,
            onDateSelected = { year, month ->
                selectedYear = year
                selectedMonth = month
            },
            onDismiss = { showMonthPicker = false }
        )
    }
}

@Composable
private fun DailyReportCard(
    dailyReport: DailyReport,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // 左侧日期
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.width(60.dp)
            ) {
                Text(
                    text = "${dailyReport.dayOfMonth}日",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF4ECDC4)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 右侧内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 描述文字
                Text(
                    text = dailyReport.description,
                    fontSize = 14.sp,
                    color = Color(0xFF333333),
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(12.dp))

                // 小红花评级
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    repeat(5) { index ->
                        val isActive = index < dailyReport.starRating
                        Text(
                            text = "🌸",
                            fontSize = 16.sp,
                            color = if (isActive) Color.Unspecified else Color.Gray.copy(alpha = 0.3f),
                            modifier = Modifier.padding(horizontal = 2.dp)
                        )
                    }
                }
            }
        }
    }
}



// 生成示例数据
private fun generateSampleDailyReports(year: Int, month: Int): List<DailyReport> {
    val descriptions = listOf(
        "今日的阿呆格外的聪明，看上去他学会了如何与朋友们相处，可以主动帮助小朋友系鞋带，记得要表扬一下他哦",
        "今天小朋友表现很棒，主动分享玩具，还帮助老师整理教室，语言表达也更加流畅了",
        "今日学习新的儿歌，能够跟着节拍拍手，还主动要求表演给大家看，自信心有很大提升",
        "今天在游戏中表现出很好的合作精神，能够耐心等待轮到自己，情绪管理能力有进步",
        "今日阅读时间专注度很高，能够完整听完一个故事，还主动提问故事内容",
        "今天在美术课上表现出色，能够独立完成作品，色彩搭配很有创意",
        "今日体育活动中积极参与，团队协作能力有明显提升，运动协调性也更好了",
        "今天学习数字游戏，能够准确数到20，逻辑思维能力有很大进步",
        "今日音乐课表现突出，能够跟着节拍唱歌，节奏感很强",
        "今天在角色扮演游戏中表现活跃，想象力丰富，表达能力也有提升"
    )

    // 获取该月的天数
    val calendar = Calendar.getInstance()
    calendar.set(year, month - 1, 1) // month - 1 因为Calendar的月份从0开始
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

    // 生成该月的前几天数据（最多10天）
    val numberOfDays = minOf(daysInMonth, 10)

    return (1..numberOfDays).map { day ->
        DailyReport(
            date = "${year}年${month}月${day}日",
            dayOfMonth = day,
            description = descriptions[(day - 1) % descriptions.size],
            starRating = when (day % 5) {
                1 -> 4
                2 -> 3
                3 -> 5
                4 -> 4
                0 -> 3
                else -> 3
            }
        )
    }
}


