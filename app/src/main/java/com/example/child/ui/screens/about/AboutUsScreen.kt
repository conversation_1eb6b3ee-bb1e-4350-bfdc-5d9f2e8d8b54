package com.example.child.ui.screens.about

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.child.R
import com.example.child.ui.components.RoundedCard
import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutUsScreen(
    onBackClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Background)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
        // 顶部导航栏
        TopAppBar(
            title = {
                Text(
                    text = "关于我们",
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color.White
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Primary
            )
        )
        // 可滚动内容
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {

            item {
                Spacer(modifier = Modifier.height(20.dp))

                // 应用信息卡片
                AppInfoCard()
            }

            item {
                // 公司信息卡片
                CompanyInfoCard()
            }

            item {
                // 联系方式卡片
                ContactInfoCard()
            }

            item {
                // 法律信息卡片
                LegalInfoCard()
            }

            item {
                Spacer(modifier = Modifier.height(40.dp))
            }
        }
    }
        }
}

@Composable
private fun AppInfoCard() {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(vertical = 24.dp, horizontal = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 应用图标
            Surface(
                modifier = Modifier.size(80.dp),
                shape = androidx.compose.foundation.shape.RoundedCornerShape(16.dp),
                color = Primary.copy(alpha = 0.1f)
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    modifier = Modifier.padding(20.dp),
                    tint = Primary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 应用名称
            Text(
                text = stringResource(id = R.string.app_name),
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = TextPrimary
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 版本信息
            Text(
                text = "版本 1.0.0",
                fontSize = 14.sp,
                color = TextSecondary
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 应用描述
            Text(
                text = "专注于儿童成长分析的智能应用，通过语音分析技术，帮助家长更好地了解孩子的成长状况。",
                fontSize = 14.sp,
                color = TextSecondary,
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
private fun CompanyInfoCard() {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            InfoItem(
                icon = Icons.Default.Home,
                title = "公司名称",
                content = "成长科技有限公司"
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            InfoItem(
                icon = Icons.Default.LocationOn,
                title = "公司地址",
                content = "北京市朝阳区科技园区创新大厦"
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            InfoItem(
                icon = Icons.Default.Info,
                title = "官方网站",
                content = "www.childgrowth.com"
            )
        }
    }
}

@Composable
private fun ContactInfoCard() {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            InfoItem(
                icon = Icons.Default.Email,
                title = "客服邮箱",
                content = "<EMAIL>"
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            InfoItem(
                icon = Icons.Default.Phone,
                title = "客服电话",
                content = "************"
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            InfoItem(
                icon = Icons.Default.Info,
                title = "服务时间",
                content = "周一至周五 9:00-18:00"
            )
        }
    }
}

@Composable
private fun LegalInfoCard() {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        SettingsItemComponent(
            icon = Icons.Default.Lock,
            title = stringResource(id = R.string.privacy_policy),
            subtitle = "查看隐私政策",
            onClick = { /* TODO: 打开隐私政策 */ }
        )

        Divider(
            modifier = Modifier.padding(vertical = 8.dp),
            color = Divider
        )

        SettingsItemComponent(
            icon = Icons.Default.Info,
            title = stringResource(id = R.string.terms_of_service),
            subtitle = "查看服务条款",
            onClick = { /* TODO: 打开服务条款 */ }
        )
    }
}

@Composable
private fun InfoItem(
    icon: ImageVector,
    title: String,
    content: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Primary,
            modifier = Modifier.size(20.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = content,
                fontSize = 14.sp,
                color = TextSecondary,
                lineHeight = 18.sp
            )
        }
    }
}

@Composable
private fun TopAppBarComponent(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .size(40.dp)
                .clip(androidx.compose.foundation.shape.CircleShape)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
    }
}

@Composable
private fun SettingsItemComponent(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Primary
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )

            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = TextSecondary
            )
        }

        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = TextSecondary
        )
    }
}