package com.example.child.ui.screens.notification

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.R
import com.example.child.ui.components.RoundedCard

import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    onBackClick: () -> Unit = {},
    viewModel: NotificationSettingsViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        viewModel.updatePermissionStatus(isGranted)
    }

    LaunchedEffect(Unit) {
        viewModel.checkNotificationPermission(context)
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Background)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "消息提醒",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Primary
                )
            )
            // 可滚动内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // 权限状态卡片
                    if (!uiState.hasNotificationPermission) {
                        PermissionCard(
                            onRequestPermission = {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                    permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                                } else {
                                    // 对于较低版本的Android，直接跳转到设置
                                    val intent =
                                        Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                            data =
                                                Uri.fromParts("package", context.packageName, null)
                                        }
                                    context.startActivity(intent)
                                }
                            }
                        )
                    }
                }

                if (uiState.hasNotificationPermission) {
                    item {
                        // 总开关
                        NotificationMasterSwitch(
                            enabled = uiState.notificationsEnabled,
                            onToggle = viewModel::toggleNotifications
                        )
                    }

                    item {
                        // 具体通知设置
                        NotificationOptionsCard(
                            uiState = uiState,
                            onGrowthRemindersToggle = viewModel::toggleGrowthReminders,
                            onAnalysisNotificationsToggle = viewModel::toggleAnalysisNotifications,
                            onDailySummaryToggle = viewModel::toggleDailySummary,
                            onWeeklyReportToggle = viewModel::toggleWeeklyReport
                        )
                    }
                }

                item {
                    Spacer(modifier = Modifier.height(40.dp))
                }
            }
        }
    }
}

@Composable
private fun PermissionCard(
    onRequestPermission: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Icon(
                imageVector = Icons.Default.Notifications,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(id = R.string.notification_permission_required),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )

            Spacer(modifier = Modifier.height(24.dp))

            Button(
                onClick = onRequestPermission,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Primary
                )
            ) {
                Text(
                    text = stringResource(id = R.string.grant_permission),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun NotificationMasterSwitch(
    enabled: Boolean,
    onToggle: (Boolean) -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Notifications,
                contentDescription = null,
                tint = Primary,
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(id = R.string.enable_notifications),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                Text(
                    text = "开启后可接收各类成长提醒",
                    fontSize = 14.sp,
                    color = TextSecondary
                )
            }

            Switch(
                checked = enabled,
                onCheckedChange = onToggle,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Primary,
                    checkedTrackColor = Primary.copy(alpha = 0.3f)
                ),
                modifier = Modifier.size(width = 32.dp, height = 20.dp)
            )
        }
    }
}

@Composable
private fun NotificationOptionsCard(
    uiState: NotificationSettingsUiState,
    onGrowthRemindersToggle: (Boolean) -> Unit,
    onAnalysisNotificationsToggle: (Boolean) -> Unit,
    onDailySummaryToggle: (Boolean) -> Unit,
    onWeeklyReportToggle: (Boolean) -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            NotificationOption(
                icon = Icons.Default.Star,
                title = stringResource(id = R.string.growth_reminders),
                subtitle = "孩子成长关键节点提醒",
                enabled = uiState.notificationsEnabled,
                checked = uiState.growthReminders,
                onToggle = onGrowthRemindersToggle
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            NotificationOption(
                icon = Icons.Default.Info,
                title = stringResource(id = R.string.analysis_notifications),
                subtitle = "语音分析完成通知",
                enabled = uiState.notificationsEnabled,
                checked = uiState.analysisNotifications,
                onToggle = onAnalysisNotificationsToggle
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            NotificationOption(
                icon = Icons.Default.DateRange,
                title = stringResource(id = R.string.daily_summary),
                subtitle = "每日成长总结推送",
                enabled = uiState.notificationsEnabled,
                checked = uiState.dailySummary,
                onToggle = onDailySummaryToggle
            )

            Divider(
                modifier = Modifier.padding(vertical = 12.dp),
                color = Divider
            )

            NotificationOption(
                icon = Icons.Default.DateRange,
                title = stringResource(id = R.string.weekly_report),
                subtitle = "每周成长报告提醒",
                enabled = uiState.notificationsEnabled,
                checked = uiState.weeklyReport,
                onToggle = onWeeklyReportToggle
            )
        }
    }
}

@Composable
private fun NotificationOption(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String,
    enabled: Boolean,
    checked: Boolean,
    onToggle: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = if (enabled) Primary else TextSecondary,
            modifier = Modifier.size(20.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = if (enabled) TextPrimary else TextSecondary
            )
            Text(
                text = subtitle,
                fontSize = 14.sp,
                color = TextSecondary
            )
        }

        Switch(
            checked = checked,
            onCheckedChange = onToggle,
            enabled = enabled,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Primary,
                checkedTrackColor = Primary.copy(alpha = 0.3f)
            ),
            modifier = Modifier.size(width = 32.dp, height = 20.dp)
        )
    }
}

@Composable
private fun TopAppBarComponent(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
    }
}
