package com.example.child.ui.screens.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import com.example.child.utils.Result
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

enum class LoginType {
    SMS, PASSWORD
}

data class LoginUiState(
    val loginType: LoginType = LoginType.SMS,
    val phone: String = "",
    val password: String = "",
    val verificationCode: String = "",
    val isLoading: Boolean = false,
    val isSendingCode: Boolean = false,
    val countdown: Int = 0,
    val phoneError: String = "",
    val passwordError: String = "",
    val codeError: String = "",
    val error: String = "",
    val navigationTarget: String? = null
)

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val sharedPreferencesManager: com.example.child.utils.SharedPreferencesManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    private var countdownJob: Job? = null

    fun updatePhone(phone: String) {
        _uiState.value = _uiState.value.copy(
            phone = phone,
            phoneError = ""
        )
    }

    fun updatePassword(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = ""
        )
    }

    fun updateVerificationCode(code: String) {
        _uiState.value = _uiState.value.copy(
            verificationCode = code,
            codeError = ""
        )
    }

    fun switchLoginType(type: LoginType) {
        _uiState.value = _uiState.value.copy(
            loginType = type,
            phoneError = "",
            passwordError = "",
            codeError = "",
            error = ""
        )
    }

    fun sendVerificationCode() {
        val phone = _uiState.value.phone

        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSendingCode = true, error = "")

            when (val result = authRepository.sendSmsCode(phone)) {
                is Result.Success -> {
                    _uiState.value = _uiState.value.copy(isSendingCode = false)
                    startCountdown()
                }
                is Result.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isSendingCode = false,
                        error = result.message
                    )
                }
                is Result.Loading -> {
                    // Already handled above
                }
            }
        }
    }

    fun login() {
        val currentState = _uiState.value
        val phone = currentState.phone

        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }

        when (currentState.loginType) {
            LoginType.SMS -> loginWithSms()
            LoginType.PASSWORD -> loginWithPassword()
        }
    }

    private fun loginWithSms() {
        val phone = _uiState.value.phone
        val code = _uiState.value.verificationCode

        if (code.isEmpty()) {
            _uiState.value = _uiState.value.copy(codeError = "请输入验证码")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")

            when (val result = authRepository.verifySmsCode(phone, code)) {
                is Result.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)

                    // 保存手机号供注册页面使用
                    sharedPreferencesManager.savePhone(phone)

                    if (result.data.isNewUser) {
                        _uiState.value = _uiState.value.copy(navigationTarget = "register")
                    } else {
                        _uiState.value = _uiState.value.copy(navigationTarget = "main")
                    }
                }
                is Result.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                is Result.Loading -> {
                    // Already handled above
                }
            }
        }
    }

    private fun loginWithPassword() {
        val phone = _uiState.value.phone
        val password = _uiState.value.password

        if (password.isEmpty()) {
            _uiState.value = _uiState.value.copy(passwordError = "请输入密码")
            return
        }

        if (password.length < 6) {
            _uiState.value = _uiState.value.copy(passwordError = "密码长度不能少于6位")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")

            try {
                // TODO: 调用实际的密码登录API
                // val result = authRepository.loginWithPassword(phone, password)

                // 模拟登录过程
                delay(1000)

                // 保存手机号
                sharedPreferencesManager.savePhone(phone)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    navigationTarget = "main"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "登录失败"
                )
            }
        }
    }

    private fun startCountdown() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            for (i in 60 downTo 1) {
                _uiState.value = _uiState.value.copy(countdown = i)
                delay(1000)
            }
            _uiState.value = _uiState.value.copy(countdown = 0)
        }
    }

    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
    }
}
