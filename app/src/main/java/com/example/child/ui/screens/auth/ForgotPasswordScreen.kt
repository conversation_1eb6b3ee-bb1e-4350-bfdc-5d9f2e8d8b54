package com.example.child.ui.screens.auth

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForgotPasswordScreen(
    onBackClick: () -> Unit = {},
    onResetSuccess: () -> Unit = {},
    viewModel: ForgotPasswordViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "忘记密码",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Primary
                )
            )

            // 可滚动内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                    
                    Text(
                        text = "重置密码",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A1A1A)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "请输入手机号，我们将发送验证码到您的手机",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                }

                item {
                    // 重置密码表单
                    ForgotPasswordForm(
                        phone = uiState.phone,
                        onPhoneChange = viewModel::updatePhone,
                        verificationCode = uiState.verificationCode,
                        onVerificationCodeChange = viewModel::updateVerificationCode,
                        newPassword = uiState.newPassword,
                        onNewPasswordChange = viewModel::updateNewPassword,
                        confirmPassword = uiState.confirmPassword,
                        onConfirmPasswordChange = viewModel::updateConfirmPassword,
                        countdown = uiState.countdown,
                        isSendingCode = uiState.isSendingCode,
                        onSendCode = viewModel::sendVerificationCode,
                        phoneError = uiState.phoneError,
                        codeError = uiState.codeError,
                        passwordError = uiState.passwordError,
                        confirmPasswordError = uiState.confirmPasswordError
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(32.dp))

                    // 重置密码按钮
                    ResetPasswordButton(
                        isLoading = uiState.isLoading,
                        onClick = {
                            viewModel.resetPassword()
                        }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }
    }

    // 显示错误信息
    uiState.error.takeIf { it.isNotEmpty() }?.let { error ->
        LaunchedEffect(error) {
            // TODO: 显示错误提示
        }
    }

    // 重置成功提示
    if (uiState.resetSuccess) {
        LaunchedEffect(uiState.resetSuccess) {
            // TODO: 显示成功提示并返回
            onResetSuccess()
        }
    }
}

@Composable
private fun ForgotPasswordForm(
    phone: String,
    onPhoneChange: (String) -> Unit,
    verificationCode: String,
    onVerificationCodeChange: (String) -> Unit,
    newPassword: String,
    onNewPasswordChange: (String) -> Unit,
    confirmPassword: String,
    onConfirmPasswordChange: (String) -> Unit,
    countdown: Int,
    isSendingCode: Boolean,
    onSendCode: () -> Unit,
    phoneError: String,
    codeError: String,
    passwordError: String,
    confirmPasswordError: String
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 手机号输入
        OutlinedTextField(
            value = phone,
            onValueChange = onPhoneChange,
            label = { Text("手机号") },
            placeholder = { Text("请输入手机号") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.White,
                unfocusedContainerColor = Color.White,
                unfocusedBorderColor = Color(0xFFEEEEEE),
            ),
            isError = phoneError.isNotEmpty()
        )
        
        if (phoneError.isNotEmpty()) {
            Text(
                text = phoneError,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp
            )
        }

        // 验证码输入
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedTextField(
                value = verificationCode,
                onValueChange = onVerificationCodeChange,
                label = { Text("验证码") },
                placeholder = { Text("请输入验证码") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedContainerColor = Color.White,
                    unfocusedContainerColor = Color.White,
                    unfocusedBorderColor = Color(0xFFEEEEEE),
                ),
                isError = codeError.isNotEmpty()
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 发送验证码按钮
            Button(
                onClick = onSendCode,
                enabled = countdown == 0 && !isSendingCode,
                modifier = Modifier
                    .width(100.dp)
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Primary,
                    contentColor = Color.White,
                    disabledContainerColor = Color.Gray,
                    disabledContentColor = Color.White
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                when {
                    isSendingCode -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                    }
                    countdown > 0 -> {
                        Text(
                            text = "${countdown}s",
                            fontSize = 12.sp
                        )
                    }
                    else -> {
                        Text(
                            text = "发送",
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
        
        if (codeError.isNotEmpty()) {
            Text(
                text = codeError,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp
            )
        }

        // 新密码输入
        var passwordVisible by remember { mutableStateOf(false) }
        
        OutlinedTextField(
            value = newPassword,
            onValueChange = onNewPasswordChange,
            label = { Text("新密码") },
            placeholder = { Text("请输入新密码") },
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Lock else Icons.Default.Lock,
                        contentDescription = if (passwordVisible) "隐藏密码" else "显示密码"
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.White,
                unfocusedContainerColor = Color.White,
                unfocusedBorderColor = Color(0xFFEEEEEE),
            ),
            isError = passwordError.isNotEmpty()
        )
        
        if (passwordError.isNotEmpty()) {
            Text(
                text = passwordError,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp
            )
        }

        // 确认密码输入
        var confirmPasswordVisible by remember { mutableStateOf(false) }
        
        OutlinedTextField(
            value = confirmPassword,
            onValueChange = onConfirmPasswordChange,
            label = { Text("确认密码") },
            placeholder = { Text("请再次输入新密码") },
            visualTransformation = if (confirmPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            trailingIcon = {
                IconButton(onClick = { confirmPasswordVisible = !confirmPasswordVisible }) {
                    Icon(
                        imageVector = if (confirmPasswordVisible) Icons.Default.Lock else Icons.Default.Lock,
                        contentDescription = if (confirmPasswordVisible) "隐藏密码" else "显示密码"
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = Color.White,
                unfocusedContainerColor = Color.White,
                unfocusedBorderColor = Color(0xFFEEEEEE),
            ),
            isError = confirmPasswordError.isNotEmpty()
        )
        
        if (confirmPasswordError.isNotEmpty()) {
            Text(
                text = confirmPasswordError,
                color = MaterialTheme.colorScheme.error,
                fontSize = 12.sp
            )
        }
    }
}

@Composable
private fun ResetPasswordButton(
    isLoading: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = !isLoading,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Primary,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
        } else {
            Text(
                text = "重置密码",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
