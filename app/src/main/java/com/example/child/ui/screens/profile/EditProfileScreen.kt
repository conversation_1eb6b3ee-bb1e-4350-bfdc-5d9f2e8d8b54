package com.example.child.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.R
import com.example.child.ui.components.*
import com.example.child.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditProfileScreen(
    onBackClick: () -> Unit,
    viewModel: EditProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    var showGenderSelector by remember { mutableStateOf(false) }


    LaunchedEffect(Unit) {
        viewModel.loadUserProfile()
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "修改个人信息",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Primary
                )
            )

            // 可滚动内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
            ) {

                item {
                    Spacer(modifier = Modifier.height(16.dp))

                    // 头像编辑区域
                    AvatarEditSection(
                        onAvatarClick = { /* TODO: 实现头像选择 */ }
                    )
                }

                item {
                    // 个人信息表单
                    PersonalInfoForm(
                        nickname = uiState.nickname,
                        onNicknameChange = viewModel::updateNickname,
                        childName = uiState.childName,
                        onChildNameChange = viewModel::updateChildName,
                        childGender = uiState.childGender,
                        onGenderClick = { showGenderSelector = true },
                        childAge = uiState.childAge,
                        onChildAgeChange = viewModel::updateChildAge
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(32.dp))

                    // 保存按钮
                    SaveButton(
                        isLoading = uiState.isLoading,
                        onClick = {
                            viewModel.saveProfile()
                        }
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }
    }

    // 性别选择对话框
    if (showGenderSelector) {
        GenderSelectorDialog(
            selectedGender = uiState.childGender,
            onGenderSelected = { gender ->
                viewModel.updateChildGender(gender)
                showGenderSelector = false
            },
            onDismiss = { showGenderSelector = false }
        )
    }



    // 显示错误信息
    uiState.error.takeIf { it.isNotEmpty() }?.let { error ->
        LaunchedEffect(error) {
            // TODO: 显示错误提示
        }
    }

    // 保存成功提示
    if (uiState.saveSuccess) {
        LaunchedEffect(uiState.saveSuccess) {
            // TODO: 显示成功提示并返回
            onBackClick()
        }
    }
}

@Composable
private fun TopAppBar(
    title: String,
    onBackClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(
            onClick = onBackClick,
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(Color.White.copy(alpha = 0.9f))
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = title,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = TextPrimary
        )
    }
}

@Composable
private fun AvatarEditSection(
    onAvatarClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(CircleShape)
                .background(Primary.copy(alpha = 0.2f))
                .clickable { onAvatarClick() },
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "头像",
                modifier = Modifier.size(40.dp),
                tint = Primary
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "修改头像",
            fontSize = 14.sp,
            color = Primary,
            modifier = Modifier.clickable { onAvatarClick() }
        )
    }
}

@Composable
private fun PersonalInfoForm(
    nickname: String,
    onNicknameChange: (String) -> Unit,
    childName: String,
    onChildNameChange: (String) -> Unit,
    childGender: String,
    onGenderClick: () -> Unit,
    childAge: String,
    onChildAgeChange: (String) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column() {
            // 昵称
            ProfileEditItem(
                label = "孩子称呼",
                value = nickname,
                placeholder = "请输入昵称",
                isEditable = true,
                onValueChange = onNicknameChange
            )

            Divider(color = Divider)

            // 性别选择
            ProfileEditItem(
                label = "选择性别",
                value = childGender,
                placeholder = "请选择性别",
                isEditable = false,
                onClick = onGenderClick,
                showArrow = true
            )

            Divider(color = Divider)

            // 孩子年龄
            ProfileEditItem(
                label = "孩子年龄",
                value = childAge,
                placeholder = "请输入孩子年龄",
                isEditable = true,
                onValueChange = onChildAgeChange
            )

            Divider(color = Divider)
        }
    }
}

@Composable
private fun SaveButton(
    isLoading: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = !isLoading,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Primary,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
        } else {
            Text(
                text = stringResource(id = R.string.save),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}
