package com.example.child.ui.screens.device

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.DeviceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class DeviceManagementUiState(
    val isLoading: Boolean = false,
    val isClearing: Boolean = false,
    val hasDevice: Boolean = false,
    val deviceName: String = "",
    val totalSpace: String = "",
    val usedSpace: String = "",
    val usedSpacePercentage: Float = 0f,
    val message: String = "",
    val error: String = ""
)

@HiltViewModel
class DeviceManagementViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DeviceManagementUiState())
    val uiState: StateFlow<DeviceManagementUiState> = _uiState.asStateFlow()
    
    fun loadDeviceInfo() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                val deviceInfo = deviceRepository.getConnectedDeviceInfo()
                if (deviceInfo != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = true,
                        deviceName = deviceInfo.name,
                        totalSpace = formatBytes(deviceInfo.totalSpace),
                        usedSpace = formatBytes(deviceInfo.usedSpace),
                        usedSpacePercentage = (deviceInfo.usedSpace.toFloat() / deviceInfo.totalSpace.toFloat() * 100)
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        hasDevice = false
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasDevice = false,
                    error = e.message ?: "加载设备信息失败"
                )
            }
        }
    }
    
    fun clearDevice() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isClearing = true, message = "")
            
            try {
                val success = deviceRepository.clearDevice()
                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        message = "设备数据已清空",
                        usedSpace = "0 B",
                        usedSpacePercentage = 0f
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isClearing = false,
                        error = "清空设备失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isClearing = false,
                    error = e.message ?: "清空设备失败"
                )
            }
        }
    }
    
    private fun formatBytes(bytes: Long): String {
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        var size = bytes.toDouble()
        var unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.size - 1) {
            size /= 1024
            unitIndex++
        }
        
        return String.format("%.1f %s", size, units[unitIndex])
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = "", error = "")
    }
}
