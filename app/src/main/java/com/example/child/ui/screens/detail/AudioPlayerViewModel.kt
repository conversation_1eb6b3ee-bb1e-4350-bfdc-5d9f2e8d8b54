package com.example.child.ui.screens.detail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.utils.UsbDeviceManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

data class AudioFile(
    val name: String,
    val path: String,
    val duration: Long = 0L,
    val size: Long = 0L
)

data class AudioPlayerUiState(
    val isLoading: Boolean = false,
    val audioFiles: List<AudioFile> = emptyList(),
    val currentFile: AudioFile? = null,
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val error: String = ""
)

@HiltViewModel
class AudioPlayerViewModel @Inject constructor(
    private val usbDeviceManager: UsbDeviceManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AudioPlayerUiState())
    val uiState: StateFlow<AudioPlayerUiState> = _uiState.asStateFlow()
    
    fun loadAudioFiles(timeSlot: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                // 扫描USB设备
                usbDeviceManager.scanForUsbDevices()
                
                // 获取USB设备列表
                usbDeviceManager.usbDevices.collect { devices ->
                    val audioFiles = mutableListOf<AudioFile>()
                    
                    devices.forEach { device ->
                        val files = usbDeviceManager.getAudioFiles(device)
                        files.forEach { file ->
                            // 过滤出mp4文件和其他音频文件
                            if (isAudioFile(file)) {
                                audioFiles.add(
                                    AudioFile(
                                        name = file.name,
                                        path = file.absolutePath,
                                        size = file.length()
                                    )
                                )
                            }
                        }
                    }
                    
                    // 如果没有找到音频文件，创建模拟数据
                    if (audioFiles.isEmpty()) {
                        audioFiles.addAll(getMockAudioFiles(timeSlot))
                    }
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        audioFiles = audioFiles
                    )
                }
                
            } catch (e: Exception) {
                // 如果出错，使用模拟数据
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    audioFiles = getMockAudioFiles(timeSlot),
                    error = ""
                )
            }
        }
    }
    
    fun selectAudioFile(audioFile: AudioFile) {
        _uiState.value = _uiState.value.copy(
            currentFile = audioFile,
            isPlaying = false,
            currentPosition = 0L,
            duration = audioFile.duration
        )
    }
    
    fun playPause() {
        val currentState = _uiState.value
        if (currentState.currentFile != null) {
            _uiState.value = _uiState.value.copy(
                isPlaying = !currentState.isPlaying
            )
            
            // TODO: 实际的播放/暂停逻辑
            if (!currentState.isPlaying) {
                startPlayback()
            } else {
                pausePlayback()
            }
        }
    }
    
    fun seekTo(position: Long) {
        _uiState.value = _uiState.value.copy(
            currentPosition = position
        )
        // TODO: 实际的跳转逻辑
    }
    
    fun stop() {
        _uiState.value = _uiState.value.copy(
            isPlaying = false,
            currentPosition = 0L
        )
        // TODO: 实际的停止逻辑
    }
    
    private fun startPlayback() {
        // TODO: 使用MediaPlayer或ExoPlayer开始播放
        // 这里可以模拟播放进度更新
        viewModelScope.launch {
            val currentFile = _uiState.value.currentFile
            if (currentFile != null) {
                // 模拟播放时长（实际应该从文件获取）
                val totalDuration = 180000L // 3分钟
                _uiState.value = _uiState.value.copy(duration = totalDuration)
                
                // 模拟播放进度更新
                while (_uiState.value.isPlaying && _uiState.value.currentPosition < totalDuration) {
                    kotlinx.coroutines.delay(1000)
                    if (_uiState.value.isPlaying) {
                        _uiState.value = _uiState.value.copy(
                            currentPosition = _uiState.value.currentPosition + 1000
                        )
                    }
                }
                
                // 播放结束
                if (_uiState.value.currentPosition >= totalDuration) {
                    _uiState.value = _uiState.value.copy(
                        isPlaying = false,
                        currentPosition = 0L
                    )
                }
            }
        }
    }
    
    private fun pausePlayback() {
        // TODO: 暂停播放逻辑
    }
    
    private fun isAudioFile(file: File): Boolean {
        val audioExtensions = listOf("mp3", "mp4", "wav", "m4a", "aac", "flac")
        val extension = file.extension.lowercase()
        return audioExtensions.contains(extension)
    }
    
    private fun getMockAudioFiles(timeSlot: String): List<AudioFile> {
        return listOf(
            AudioFile(
                name = "${timeSlot}_活动记录.mp4",
                path = "/mock/path/${timeSlot}_activity.mp4",
                duration = 180000L, // 3分钟
                size = 5242880L // 5MB
            ),
            AudioFile(
                name = "${timeSlot}_语音分析.mp4",
                path = "/mock/path/${timeSlot}_analysis.mp4",
                duration = 120000L, // 2分钟
                size = 3145728L // 3MB
            ),
            AudioFile(
                name = "${timeSlot}_环境音.mp3",
                path = "/mock/path/${timeSlot}_ambient.mp3",
                duration = 300000L, // 5分钟
                size = 7340032L // 7MB
            )
        )
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
}
