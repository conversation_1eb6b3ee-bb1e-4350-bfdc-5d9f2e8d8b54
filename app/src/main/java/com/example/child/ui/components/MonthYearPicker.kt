package com.example.child.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MonthYearPickerDialog(
    selectedYear: Int,
    selectedMonth: Int,
    onDateSelected: (Int, Int) -> Unit,
    onDismiss: () -> Unit
) {
    var currentYear by remember { mutableStateOf(selectedYear) }
    var currentMonth by remember { mutableStateOf(selectedMonth) }
    var showYearPicker by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // 标题
                Text(
                    text = "选择月份",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333),
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(24.dp))

                if (showYearPicker) {
                    // 年份选择器
                    YearPickerContent(
                        selectedYear = currentYear,
                        onYearSelected = { year ->
                            currentYear = year
                            showYearPicker = false
                        },
                        onBack = { showYearPicker = false }
                    )
                } else {
                    // 月份选择器
                    MonthPickerContent(
                        selectedYear = currentYear,
                        selectedMonth = currentMonth,
                        onMonthSelected = { month ->
                            currentMonth = month
                        },
                        onYearClick = { showYearPicker = true }
                    )
                }

                Spacer(modifier = Modifier.height(24.dp))

                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF666666)
                        )
                    ) {
                        Text("取消")
                    }

                    Button(
                        onClick = {
                            onDateSelected(currentYear, currentMonth)
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4ECDC4)
                        )
                    ) {
                        Text("确定", color = Color.White)
                    }
                }
            }
        }
    }
}

@Composable
private fun MonthPickerContent(
    selectedYear: Int,
    selectedMonth: Int,
    onMonthSelected: (Int) -> Unit,
    onYearClick: () -> Unit
) {
    Column {
        // 年份选择行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onYearClick() }
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${selectedYear}年",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4ECDC4)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = "选择年份",
                tint = Color(0xFF4ECDC4),
                modifier = Modifier.size(20.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 月份网格
        val months = listOf(
            "1月", "2月", "3月", "4月",
            "5月", "6月", "7月", "8月",
            "9月", "10月", "11月", "12月"
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            for (row in 0..2) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    for (col in 0..3) {
                        val monthIndex = row * 4 + col
                        val month = monthIndex + 1
                        val isSelected = month == selectedMonth

                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(
                                    if (isSelected) Color(0xFF4ECDC4)
                                    else Color(0xFFF5F5F5)
                                )
                                .clickable { onMonthSelected(month) },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = months[monthIndex],
                                fontSize = 14.sp,
                                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                color = if (isSelected) Color.White else Color(0xFF333333)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun YearPickerContent(
    selectedYear: Int,
    onYearSelected: (Int) -> Unit,
    onBack: () -> Unit
) {
    val currentYear = Calendar.getInstance().get(Calendar.YEAR)
    val years = (currentYear - 5..currentYear + 5).toList()

    Column {
        // 返回按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onBack() }
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowLeft,
                contentDescription = "返回",
                tint = Color(0xFF4ECDC4),
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "选择年份",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF4ECDC4)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 年份列表
        LazyColumn(
            modifier = Modifier.height(200.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(years) { year ->
                val isSelected = year == selectedYear

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            if (isSelected) Color(0xFF4ECDC4)
                            else Color(0xFFF5F5F5)
                        )
                        .clickable { onYearSelected(year) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "${year}年",
                        fontSize = 16.sp,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        color = if (isSelected) Color.White else Color(0xFF333333)
                    )
                }
            }
        }
    }
}
