package com.example.child.ui.screens.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class BindPhoneUiState(
    val phone: String = "",
    val verificationCode: String = "",
    val isLoading: Boolean = false,
    val isSendingCode: Boolean = false,
    val countdown: Int = 0,
    val phoneError: String = "",
    val codeError: String = "",
    val error: String = "",
    val bindSuccess: Boolean = false
)

@HiltViewModel
class BindPhoneViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(BindPhoneUiState())
    val uiState: StateFlow<BindPhoneUiState> = _uiState.asStateFlow()
    
    private var countdownJob: Job? = null
    
    fun updatePhone(phone: String) {
        _uiState.value = _uiState.value.copy(
            phone = phone,
            phoneError = ""
        )
    }
    
    fun updateVerificationCode(code: String) {
        _uiState.value = _uiState.value.copy(
            verificationCode = code,
            codeError = ""
        )
    }
    
    fun sendVerificationCode() {
        val phone = _uiState.value.phone
        
        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSendingCode = true, error = "")
            
            try {
                // TODO: 调用实际的API发送验证码
                // authRepository.sendSmsCode(phone)
                
                // 模拟发送过程
                delay(1000)
                
                _uiState.value = _uiState.value.copy(isSendingCode = false)
                startCountdown()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSendingCode = false,
                    error = e.message ?: "发送验证码失败"
                )
            }
        }
    }
    
    fun bindPhone() {
        val phone = _uiState.value.phone
        val code = _uiState.value.verificationCode
        
        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }
        
        if (code.isEmpty()) {
            _uiState.value = _uiState.value.copy(codeError = "请输入验证码")
            return
        }
        
        if (code.length != 6) {
            _uiState.value = _uiState.value.copy(codeError = "验证码格式不正确")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                // TODO: 调用实际的API绑定手机号
                // authRepository.bindPhone(phone, code)
                
                // 模拟绑定过程
                delay(1000)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    bindSuccess = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "绑定手机号失败"
                )
            }
        }
    }
    
    private fun startCountdown() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            for (i in 60 downTo 1) {
                _uiState.value = _uiState.value.copy(countdown = i)
                delay(1000)
            }
            _uiState.value = _uiState.value.copy(countdown = 0)
        }
    }
    
    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
    
    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
    }
}
