package com.example.child.ui.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class HomeUiState(
    val isLoading: Boolean = false,
    val hasData: Boolean = false,
    val audioRecords: List<Any> = emptyList(), // TODO: 替换为实际的AudioRecord类型
    val error: String = ""
)

@HiltViewModel
class HomeViewModel @Inject constructor(
    val dataManager: com.example.child.data.manager.TimelineDataManager,
    val assetManager: com.example.child.utils.AssetManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    fun loadTodayData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")

            try {
                val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())

                // TODO: 从实际的数据仓库加载数据
                // 模拟加载过程
                kotlinx.coroutines.delay(1500)

                // 模拟有数据的情况
                val hasData = true // 可以改为 false 测试无数据状态
                val mockAudioRecords = if (hasData) {
                    listOf("record1", "record2", "record3") // 模拟数据
                } else {
                    emptyList()
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasData = hasData,
                    audioRecords = mockAudioRecords
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }
}
