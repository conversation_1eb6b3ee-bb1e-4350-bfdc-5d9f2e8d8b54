package com.example.child.ui.screens.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.data.manager.TimelineDataManager
import com.example.child.utils.AssetManager
import com.example.child.utils.rememberAssetImage
import androidx.compose.foundation.Image
import androidx.compose.ui.layout.ContentScale
import java.text.SimpleDateFormat
import java.util.*

// 时间轴数据类
data class TimelineItem(
    val time: String,
    val status: String,
    val description: String,
    val summary: String,
    val iconColor: Color
)

@Composable
fun HomeScreen(
    onNavigateToDetail: (String) -> Unit = {},
    dataManager: TimelineDataManager = hiltViewModel<HomeViewModel>().dataManager,
    assetManager: AssetManager = hiltViewModel<HomeViewModel>().assetManager
) {
    var selectedDate by remember { mutableStateOf("2024年1月15日") }
    var timelineItems by remember { mutableStateOf<List<TimelineItem>>(emptyList()) }
    var statisticsData by remember { mutableStateOf(Triple("8小时", "8小时", "8小时")) }
    var isLoading by remember { mutableStateOf(true) }

    // 当日期改变时重新加载数据
    LaunchedEffect(selectedDate) {
        isLoading = true
        dataManager.getTimelineData(selectedDate).collect { items ->
            timelineItems = items
            isLoading = false
        }
    }

    // 加载统计数据
    LaunchedEffect(selectedDate) {
        dataManager.getStatisticsData(selectedDate).collect { stats ->
            statisticsData = stats
        }
    }

    // 加载背景图片
    val backgroundImage = rememberAssetImage(
        fileName = "background.png",
        folder = AssetManager.ImagePaths.BACKGROUNDS,
        assetManager = assetManager
    )

    Box(
        modifier = Modifier.fillMaxSize().background(Color(0xFFF0F0F0))
    ) {
        // 背景图片或渐变背景
        if (backgroundImage != null) {
            Image(
                bitmap = backgroundImage,
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop
            )
        } else {
            // 如果图片加载失败，使用原来的渐变背景
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFF4ECDC4),
                                Color(0xFF44A08D),
                                Color(0xFFF9FAFB)
                            )
                        )
                    )
            )
        }
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 沉浸式状态栏间距
                Spacer(modifier = Modifier.height(60.dp))

                // 顶部用户信息和日期选择
                TopHeaderSection(
                    userName = "今日的阿呆",
                    selectedDate = selectedDate,
                    onDateChange = { selectedDate = it }
                )
            }

            item {
                // 描述文字
                Text(
                    text = "今日的阿呆格外的聪明，看上去他学会了如何与朋友们相处，可主动帮助小朋友系鞋带，记得要表扬一下他哦",
                    color = Color.White,
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            item {
                // 三个统计卡片
                StatisticsCards(
                    activityTime = statisticsData.first,
                    studyTime = statisticsData.second,
                    exerciseTime = statisticsData.third
                )
            }

            // 时间轴
            if (isLoading) {
                item {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = Color.White)
                    }
                }
            } else {
                items(timelineItems) { item ->
                    TimelineItemCard(
                        item = item,
                        onViewDetails = {
                            onNavigateToDetail(item.time)
                        }
                    )
                }
            }

            item {
                // 沉浸式底部导航栏间距
                Spacer(modifier = Modifier.height(120.dp))
            }
        }
    }
}

@Composable
fun TopHeaderSection(
    userName: String,
    selectedDate: String,
    onDateChange: (String) -> Unit
) {
    val context = LocalContext.current
    var showDatePicker by remember { mutableStateOf(false) }

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧用户名
        Text(
            text = userName,
            color = Color.White,
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold
        )

        // 右侧日期选择器
        Card(
            modifier = Modifier.clickable {
                showDatePicker = true
            },
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = selectedDate,
                    color = Color(0xFF333333),
                    fontSize = 14.sp
                )
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = "选择日期",
                    tint = Color(0xFF666666),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }

    // 日期选择器对话框
    if (showDatePicker) {
        DatePickerDialog(
            onDateSelected = { selectedDateMillis ->
                selectedDateMillis?.let {
                    val formatter = SimpleDateFormat("yyyy年M月d日", Locale.getDefault())
                    val date = Date(it)
                    onDateChange(formatter.format(date))
                }
                showDatePicker = false
            },
            onDismiss = { showDatePicker = false }
        )
    }
}

@Composable
fun StatisticsCards(
    activityTime: String = "8小时",
    studyTime: String = "8小时",
    exerciseTime: String = "8小时"
) {
    Column(
        modifier = Modifier.padding(bottom = 20.dp)
    ){
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f))
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    value = activityTime,
                    label = "活动时长",
                    color = Color(0xFFFF6B6B)
                )
                StatisticItem(
                    value = studyTime,
                    label = "学习时间",
                    color = Color(0xFF4ECDC4)
                )
                StatisticItem(
                    value = exerciseTime,
                    label = "运动时间",
                    color = Color(0xFF45B7D1)
                )
            }
        }
    }
}

@Composable
fun StatisticItem(
    value: String,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}

@Composable
fun TimelineItemCard(
    item: TimelineItem,
    onViewDetails: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        // 左侧时间轴图标
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(CircleShape)
                    .background(item.iconColor),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = item.time.substring(0, 2), // 显示小时
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            // 连接线（除了最后一个项目）
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .fillMaxHeight()
                    .background(Color(0xFFE0E0E0))
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        // 右侧内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.9f))
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    // 时间和状态
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = item.time,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF333333)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = item.status,
                            fontSize = 14.sp,
                            color = Color(0xFF666666)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 描述
                    Text(
                        text = item.description,
                        fontSize = 14.sp,
                        color = Color(0xFF333333),
                        lineHeight = 20.sp
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // 时段总结（灰底块）
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "时段总结",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF666666)
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = item.summary,
                                fontSize = 13.sp,
                                color = Color(0xFF333333),
                                lineHeight = 18.sp
                            )
                        }
                    }
                    // 查看详情链接
                    Text(
                        text = "查看详情 >",
                        fontSize = 13.sp,
                        color = Color(0xFFFF6B6B),
                        modifier = Modifier
                            .clickable { onViewDetails() }
                            .align(Alignment.End)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    onDateSelected: (Long?) -> Unit,
    onDismiss: () -> Unit
) {
    val datePickerState = rememberDatePickerState()

    DatePickerDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(onClick = {
                onDateSelected(datePickerState.selectedDateMillis)
            }) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    ) {
        DatePicker(state = datePickerState)
    }
}