package com.example.child.ui.theme

import androidx.compose.ui.graphics.Color

// 主色调 - 温暖的蓝紫色系
val Primary = Color(0xFF6B73FF)
val PrimaryVariant = Color(0xFF5A63E8)
val Secondary = Color(0xFFFF9F43)
val SecondaryVariant = Color(0xFFFF8C2A)

// 背景色 - 温馨的浅色调
val Background = Color(0xFFF8F9FA)
val Surface = Color(0xFFFFFFFF)
val SurfaceVariant = Color(0xFFF5F5F5)

// 文字颜色
val OnPrimary = Color(0xFFFFFFFF)
val OnSecondary = Color(0xFFFFFFFF)
val OnBackground = Color(0xFF1A1A1A)
val OnSurface = Color(0xFF1A1A1A)
val TextPrimary = Color(0xFF1A1A1A)
val TextSecondary = Color(0xFF666666)
val TextHint = Color(0xFF999999)

// 状态颜色
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// 通用颜色
val Divider = Color(0xFFE0E0E0)
val Shadow = Color(0x1A000000)

// 渐变色 - 儿童友好的渐变
val GradientStart = Color(0xFF6B73FF)
val GradientEnd = Color(0xFF9C27B0)
val GradientLight = Color(0xFFE8F4FD)

// 儿童成长主题色
val GrowthGreen = Color(0xFF4CAF50)
val HappinessYellow = Color(0xFFFFC107)
val LearningBlue = Color(0xFF2196F3)
val CreativityPurple = Color(0xFF9C27B0)
