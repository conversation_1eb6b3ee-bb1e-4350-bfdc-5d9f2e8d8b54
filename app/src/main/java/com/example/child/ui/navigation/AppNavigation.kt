package com.example.child.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.child.ui.screens.splash.SplashScreen
import com.example.child.ui.screens.auth.LoginScreen
import com.example.child.ui.screens.auth.RegisterScreen
import com.example.child.ui.screens.auth.ForgotPasswordScreen
import com.example.child.ui.screens.main.MainScreen

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = "login"
    ) {
//        // 启动页 - 开屏页面
//        composable("splash") {
//            SplashScreen(navController)
//        }

        // 登录页 - 短信验证登录
        composable("login") {
            LoginScreen(navController)
        }

        // 注册页 - 新用户信息完善
        composable("register") {
            RegisterScreen(navController)
        }

        // 忘记密码页 - 通过手机号重置密码
        composable("forgot_password") {
            ForgotPasswordScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onResetSuccess = {
                    navController.popBackStack()
                }
            )
        }

        // 主页面 - 包含底部导航的主界面
        composable("main") {
            MainScreen()
        }
    }
}
