package com.example.child.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.ui.screens.detail.AudioFile
import com.example.child.ui.screens.detail.AudioPlayerViewModel
import com.example.child.ui.theme.Primary

@Composable
fun AudioPlayer(
    timeSlot: String,
    modifier: Modifier = Modifier,
    viewModel: AudioPlayerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(timeSlot) {
        viewModel.loadAudioFiles(timeSlot)
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "音频播放器",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                Icon(
                    imageVector = Icons.Default.AudioFile,
                    contentDescription = "音频文件",
                    tint = Primary,
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (uiState.isLoading) {
                // 加载状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Primary,
                        modifier = Modifier.size(32.dp)
                    )
                }
            } else if (uiState.audioFiles.isEmpty()) {
                // 无音频文件状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.MusicNote,
                            contentDescription = "无音频文件",
                            tint = Color(0xFF999999),
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "未找到音频文件",
                            color = Color(0xFF999999),
                            fontSize = 14.sp
                        )
                    }
                }
            } else {
                // 音频文件列表
                Column {
                    // 当前播放的文件信息
                    uiState.currentFile?.let { currentFile ->
                        CurrentPlayingCard(
                            audioFile = currentFile,
                            isPlaying = uiState.isPlaying,
                            currentPosition = uiState.currentPosition,
                            duration = uiState.duration,
                            onPlayPause = viewModel::playPause,
                            onSeek = viewModel::seekTo,
                            onStop = viewModel::stop
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Divider(color = Color(0xFFEEEEEE))
                        
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                    
                    // 文件列表标题
                    Text(
                        text = "音频文件列表",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF666666)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 音频文件列表
                    uiState.audioFiles.take(3).forEach { audioFile ->
                        AudioFileItem(
                            audioFile = audioFile,
                            isSelected = audioFile == uiState.currentFile,
                            onClick = { viewModel.selectAudioFile(audioFile) }
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                    
                    if (uiState.audioFiles.size > 3) {
                        Text(
                            text = "还有 ${uiState.audioFiles.size - 3} 个文件...",
                            fontSize = 12.sp,
                            color = Color(0xFF999999),
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun CurrentPlayingCard(
    audioFile: AudioFile,
    isPlaying: Boolean,
    currentPosition: Long,
    duration: Long,
    onPlayPause: () -> Unit,
    onSeek: (Long) -> Unit,
    onStop: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 文件名
            Text(
                text = audioFile.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 播放控制
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 播放/暂停按钮
                IconButton(
                    onClick = onPlayPause,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Primary)
                ) {
                    Icon(
                        imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                        contentDescription = if (isPlaying) "暂停" else "播放",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 进度条
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    val progress = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f
                    
                    Slider(
                        value = progress,
                        onValueChange = { newProgress ->
                            onSeek((newProgress * duration).toLong())
                        },
                        colors = SliderDefaults.colors(
                            thumbColor = Primary,
                            activeTrackColor = Primary,
                            inactiveTrackColor = Color(0xFFE0E0E0)
                        ),
                        modifier = Modifier.height(20.dp)
                    )
                    
                    // 时间显示
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = formatTime(currentPosition),
                            fontSize = 10.sp,
                            color = Color(0xFF666666)
                        )
                        Text(
                            text = formatTime(duration),
                            fontSize = 10.sp,
                            color = Color(0xFF666666)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // 停止按钮
                IconButton(
                    onClick = onStop,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Stop,
                        contentDescription = "停止",
                        tint = Color(0xFF666666),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun AudioFileItem(
    audioFile: AudioFile,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                color = if (isSelected) Color(0xFFE3F2FD) else Color.Transparent,
                shape = RoundedCornerShape(6.dp)
            )
            .padding(horizontal = 8.dp, vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = when (audioFile.name.substringAfterLast('.').lowercase()) {
                "mp4" -> Icons.Default.VideoLibrary
                "mp3", "m4a", "aac" -> Icons.Default.AudioFile
                else -> Icons.Default.Description
            },
            contentDescription = "音频文件",
            tint = if (isSelected) Primary else Color(0xFF666666),
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = audioFile.name,
                fontSize = 12.sp,
                color = if (isSelected) Primary else Color(0xFF333333),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = formatFileSize(audioFile.size),
                fontSize = 10.sp,
                color = Color(0xFF999999)
            )
        }
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "当前选中",
                tint = Primary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60
    return String.format("%02d:%02d", minutes, seconds)
}

private fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    return when {
        mb >= 1 -> String.format("%.1f MB", mb)
        kb >= 1 -> String.format("%.1f KB", kb)
        else -> "$bytes B"
    }
}
