package com.example.child.ui.screens.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import com.example.child.utils.Result
import com.example.child.utils.SharedPreferencesManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class RegisterUiState(
    val userName: String = "",
    val childName: String = "",
    val childAge: String = "",
    val childGender: String = "",
    val isLoading: Boolean = false,
    val userNameError: String = "",
    val childNameError: String = "",
    val childAgeError: String = "",
    val childGenderError: String = "",
    val error: String = "",
    val navigationTarget: String? = null
)

@HiltViewModel
class RegisterViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val sharedPreferencesManager: SharedPreferencesManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()
    
    fun updateUserName(name: String) {
        _uiState.value = _uiState.value.copy(
            userName = name,
            userNameError = ""
        )
    }
    
    fun updateChildName(name: String) {
        _uiState.value = _uiState.value.copy(
            childName = name,
            childNameError = ""
        )
    }
    
    fun updateChildAge(age: String) {
        _uiState.value = _uiState.value.copy(
            childAge = age,
            childAgeError = ""
        )
    }
    
    fun updateChildGender(gender: String) {
        _uiState.value = _uiState.value.copy(
            childGender = gender,
            childGenderError = ""
        )
    }
    
    fun register() {
        if (!validateInput()) return
        
        val phone = sharedPreferencesManager.getPhone() ?: return
        val state = _uiState.value
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            when (val result = authRepository.register(
                phone = phone,
                name = state.userName,
                childName = state.childName,
                childAge = state.childAge.toInt(),
                childGender = state.childGender
            )) {
                is Result.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        navigationTarget = "main"
                    )
                }
                is Result.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                is Result.Loading -> {
                    // Already handled above
                }
            }
        }
    }
    
    private fun validateInput(): Boolean {
        val state = _uiState.value
        var hasError = false
        
        val userNameError = if (state.userName.isBlank()) {
            hasError = true
            "请输入您的姓名"
        } else ""
        
        val childNameError = if (state.childName.isBlank()) {
            hasError = true
            "请输入孩子的姓名"
        } else ""
        
        val childAgeError = if (state.childAge.isBlank()) {
            hasError = true
            "请输入孩子的年龄"
        } else if (state.childAge.toIntOrNull() == null || state.childAge.toInt() < 0 || state.childAge.toInt() > 18) {
            hasError = true
            "请输入有效的年龄（0-18岁）"
        } else ""
        
        val childGenderError = if (state.childGender.isBlank()) {
            hasError = true
            "请选择孩子的性别"
        } else ""
        
        _uiState.value = _uiState.value.copy(
            userNameError = userNameError,
            childNameError = childNameError,
            childAgeError = childAgeError,
            childGenderError = childGenderError
        )
        
        return !hasError
    }
}
