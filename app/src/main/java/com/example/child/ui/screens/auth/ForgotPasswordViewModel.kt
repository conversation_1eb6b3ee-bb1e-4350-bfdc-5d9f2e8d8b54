package com.example.child.ui.screens.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ForgotPasswordUiState(
    val phone: String = "",
    val verificationCode: String = "",
    val newPassword: String = "",
    val confirmPassword: String = "",
    val isLoading: Boolean = false,
    val isSendingCode: Boolean = false,
    val countdown: Int = 0,
    val phoneError: String = "",
    val codeError: String = "",
    val passwordError: String = "",
    val confirmPasswordError: String = "",
    val error: String = "",
    val resetSuccess: Boolean = false
)

@HiltViewModel
class ForgotPasswordViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ForgotPasswordUiState())
    val uiState: StateFlow<ForgotPasswordUiState> = _uiState.asStateFlow()
    
    private var countdownJob: Job? = null
    
    fun updatePhone(phone: String) {
        _uiState.value = _uiState.value.copy(
            phone = phone,
            phoneError = ""
        )
    }
    
    fun updateVerificationCode(code: String) {
        _uiState.value = _uiState.value.copy(
            verificationCode = code,
            codeError = ""
        )
    }
    
    fun updateNewPassword(password: String) {
        _uiState.value = _uiState.value.copy(
            newPassword = password,
            passwordError = ""
        )
    }
    
    fun updateConfirmPassword(password: String) {
        _uiState.value = _uiState.value.copy(
            confirmPassword = password,
            confirmPasswordError = ""
        )
    }
    
    fun sendVerificationCode() {
        val phone = _uiState.value.phone
        
        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSendingCode = true, error = "")
            
            try {
                // TODO: 调用实际的API发送验证码
                // authRepository.sendSmsCode(phone)
                
                // 模拟发送过程
                delay(1000)
                
                _uiState.value = _uiState.value.copy(isSendingCode = false)
                startCountdown()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSendingCode = false,
                    error = e.message ?: "发送验证码失败"
                )
            }
        }
    }
    
    fun resetPassword() {
        val currentState = _uiState.value
        val phone = currentState.phone
        val code = currentState.verificationCode
        val newPassword = currentState.newPassword
        val confirmPassword = currentState.confirmPassword
        
        // 验证输入
        if (!isValidPhone(phone)) {
            _uiState.value = _uiState.value.copy(phoneError = "手机号格式不正确")
            return
        }
        
        if (code.isEmpty()) {
            _uiState.value = _uiState.value.copy(codeError = "请输入验证码")
            return
        }
        
        if (code.length != 6) {
            _uiState.value = _uiState.value.copy(codeError = "验证码格式不正确")
            return
        }
        
        if (newPassword.isEmpty()) {
            _uiState.value = _uiState.value.copy(passwordError = "请输入新密码")
            return
        }
        
        if (newPassword.length < 6) {
            _uiState.value = _uiState.value.copy(passwordError = "密码长度不能少于6位")
            return
        }
        
        if (confirmPassword.isEmpty()) {
            _uiState.value = _uiState.value.copy(confirmPasswordError = "请确认新密码")
            return
        }
        
        if (newPassword != confirmPassword) {
            _uiState.value = _uiState.value.copy(confirmPasswordError = "两次输入的密码不一致")
            return
        }
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                // TODO: 调用实际的API重置密码
                // authRepository.resetPassword(phone, code, newPassword)
                
                // 模拟重置过程
                delay(1000)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    resetSuccess = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "重置密码失败"
                )
            }
        }
    }
    
    private fun startCountdown() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            for (i in 60 downTo 1) {
                _uiState.value = _uiState.value.copy(countdown = i)
                delay(1000)
            }
            _uiState.value = _uiState.value.copy(countdown = 0)
        }
    }
    
    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
    
    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
    }
}
