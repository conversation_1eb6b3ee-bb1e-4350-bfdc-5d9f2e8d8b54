package com.example.child.ui.screens.monthly

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.model.DailyAnalysis
import com.example.child.data.model.MonthlyReport
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

data class MonthlyUiState(
    val isLoading: Boolean = false,
    val selectedYear: Int = Calendar.getInstance().get(Calendar.YEAR),
    val selectedMonth: Int = Calendar.getInstance().get(Calendar.MONTH) + 1,
    val monthlyReport: MonthlyReport? = null,
    val dailyAnalysisList: List<DailyAnalysis> = emptyList(),
    val error: String = ""
)

@HiltViewModel
class MonthlyViewModel @Inject constructor(
    // TODO: Add repositories when created
    val assetManager: com.example.child.utils.AssetManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MonthlyUiState())
    val uiState: StateFlow<MonthlyUiState> = _uiState.asStateFlow()
    
    fun loadMonthlyData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                // TODO: Load actual data from repositories
                // For now, simulate loading
                kotlinx.coroutines.delay(1000)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    monthlyReport = null, // Will be loaded from repository
                    dailyAnalysisList = emptyList() // Will be loaded from repository
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载数据失败"
                )
            }
        }
    }
    
    fun selectMonth(year: Int, month: Int) {
        _uiState.value = _uiState.value.copy(
            selectedYear = year,
            selectedMonth = month
        )
        loadMonthlyData()
    }
}
