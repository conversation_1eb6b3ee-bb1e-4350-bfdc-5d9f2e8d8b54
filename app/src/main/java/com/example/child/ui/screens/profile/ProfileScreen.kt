package com.example.child.ui.screens.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.R
import com.example.child.ui.components.*
import com.example.child.ui.theme.*

@Composable
fun ProfileScreen(
    onNavigateToEditProfile: () -> Unit = {},
    onNavigateToChangePassword: () -> Unit = {},
    onNavigateToDeviceManagement: () -> Unit = {},
    onNavigateToAboutUs: () -> Unit = {},
    onNavigateToNotificationSettings: () -> Unit = {},
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.loadUserProfile()
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // 渐变遮罩
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        0f to Color(0xFFFFE389),
                        0.3f to Color(0xFFF0F0F0),
                    )
                )
        )

        // 可滚动内容
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Spacer(modifier = Modifier.height(60.dp))

                // 用户信息卡片
                UserProfileCard(
                    user = uiState.user,
                    onEditClick = { /* TODO: Navigate to edit profile */ }
                )
            }

            item {
                // 设置选项
                SettingsSection(
                    onNavigateToEditProfile = onNavigateToEditProfile,
                    onNavigateToChangePassword = onNavigateToChangePassword
                )
            }

            item {
                // 设备管理和通知设置
                DeviceManagementCard(
                    onDeviceManagementClick = onNavigateToDeviceManagement,
                    onNotificationSettingsClick = onNavigateToNotificationSettings
                )
            }

            item {
                // 关于我们
                AboutCard(
                    onAboutUsClick = onNavigateToAboutUs
                )
            }

            item {
                Spacer(modifier = Modifier.height(100.dp))
            }
        }
    }
}

@Composable
private fun UserProfileCard(
    user: Any?, // TODO: Replace with actual User type
    onEditClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像
            Surface(
                modifier = Modifier.size(64.dp),
                shape = CircleShape,
                color = Primary.copy(alpha = 0.2f)
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    modifier = Modifier.padding(16.dp),
                    tint = Primary
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "张三", // TODO: Use actual user name
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = TextPrimary
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "138****8888", // TODO: Use actual phone
                    fontSize = 14.sp,
                    color = TextSecondary
                )
            }
        }
    }
}

@Composable
private fun SettingsSection(
    onNavigateToEditProfile: () -> Unit,
    onNavigateToChangePassword: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {

        SettingsItem(
            icon = Icons.Default.Person,
            title = stringResource(id = R.string.profile_settings),
            subtitle = "修改个人信息",
            onClick = onNavigateToEditProfile
        )

        Divider(
            modifier = Modifier.padding(vertical = 8.dp),
            color = Divider
        )

        SettingsItem(
            icon = Icons.Default.Lock,
            title = "账号与安全",
            subtitle = "账号设置管理",
            onClick = onNavigateToChangePassword
        )
    }
}

@Composable
private fun DeviceManagementCard(
    onDeviceManagementClick: () -> Unit,
    onNotificationSettingsClick: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        SettingsItem(
            icon = Icons.Default.Settings,
            title = stringResource(id = R.string.device_management),
            subtitle = "管理USB设备和存储空间",
            onClick = onDeviceManagementClick
        )
        Divider(
            modifier = Modifier.padding(vertical = 8.dp),
            color = Divider
        )
        SettingsItem(
            icon = Icons.Default.Notifications,
            title = stringResource(id = R.string.notification_settings),
            subtitle = "设置消息提醒和通知",
            onClick = onNotificationSettingsClick
        )
    }
}

@Composable
private fun AboutCard(
    onAboutUsClick: () -> Unit
) {
    RoundedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        SettingsItem(
            icon = Icons.Default.Info,
            title = stringResource(id = R.string.about_us),
            subtitle = "关于我们和应用信息",
            onClick = onAboutUsClick
        )
    }
}


@Composable
private fun SettingsItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = Primary
        )

        Spacer(modifier = Modifier.width(16.dp))

        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = TextPrimary
            )

            Text(
                text = subtitle,
                fontSize = 12.sp,
                color = TextSecondary
            )
        }

        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = TextSecondary
        )
    }
}
