package com.example.child.ui.screens.detail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.child.data.manager.TimelineDataManager

// 详细活动数据类
data class ActivityDetail(
    val time: String,
    val activity: String,
    val description: String,
    val emotion: String,
    val recommendation: String
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HourlyDetailScreen(
    timeSlot: String = "08:00",
    onBackClick: () -> Unit = {},
    dataManager: TimelineDataManager = hiltViewModel<DetailViewModel>().dataManager
) {
    var activityDetails by remember { mutableStateOf<List<ActivityDetail>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    // 加载详细数据
    LaunchedEffect(timeSlot) {
        isLoading = true
        dataManager.getHourlyDetails(timeSlot).collect { details ->
            activityDetails = details
            isLoading = false
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF4ECDC4),
                        Color(0xFF44A08D),
                        Color(0xFFF9FAFB)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            TopAppBar(
                title = {
                    Text(
                        text = "${timeSlot} 时段详情",
                        color = Color.White,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.Transparent
                )
            )

            // 详情内容
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // 时段总览卡片
                    TimeSlotOverviewCard(timeSlot = timeSlot)
                }

                if (isLoading) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(color = Color.White)
                        }
                    }
                } else {
                    items(activityDetails) { detail ->
                        ActivityDetailCard(detail = detail)
                    }
                }

                item {
                    Spacer(modifier = Modifier.height(100.dp))
                }
            }
        }
    }
}

@Composable
fun TimeSlotOverviewCard(timeSlot: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "时段总览",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )
            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                OverviewItem(
                    label = "活动数量",
                    value = "4项",
                    color = Color(0xFF4ECDC4)
                )
                OverviewItem(
                    label = "情绪状态",
                    value = "良好",
                    color = Color(0xFF45B7D1)
                )
                OverviewItem(
                    label = "完成度",
                    value = "100%",
                    color = Color(0xFF96CEB4)
                )
            }
        }
    }
}

@Composable
fun OverviewItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}

@Composable
fun ActivityDetailCard(detail: ActivityDetail) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 时间和活动标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = detail.time,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF4ECDC4)
                )
                Card(
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = when (detail.emotion) {
                            "愉悦" -> Color(0xFFE8F5E8)
                            "满足" -> Color(0xFFE3F2FD)
                            "专注" -> Color(0xFFFFF3E0)
                            "期待" -> Color(0xFFF3E5F5)
                            else -> Color(0xFFF5F5F5)
                        }
                    )
                ) {
                    Text(
                        text = detail.emotion,
                        fontSize = 12.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = detail.activity,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = detail.description,
                fontSize = 14.sp,
                color = Color(0xFF666666),
                lineHeight = 20.sp
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 建议区域
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F8FF))
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = "AI 建议",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF4ECDC4)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = detail.recommendation,
                        fontSize = 13.sp,
                        color = Color(0xFF333333),
                        lineHeight = 18.sp
                    )
                }
            }
        }
    }
}
