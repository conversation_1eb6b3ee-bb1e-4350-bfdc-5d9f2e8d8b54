package com.example.child.ui.screens.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class EditProfileUiState(
    val isLoading: Boolean = false,
    val nickname: String = "",
    val childName: String = "",
    val childGender: String = "",
    val childAge: String = "",
    val error: String = "",
    val saveSuccess: Boolean = false
)

@HiltViewModel
class EditProfileViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(EditProfileUiState())
    val uiState: StateFlow<EditProfileUiState> = _uiState.asStateFlow()

    fun loadUserProfile() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")

            try {
                authRepository.getCurrentUser().collect { user ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        nickname = user?.name ?: "",
                        childName = user?.childName ?: "",
                        childGender = user?.childGender ?: "",
                        childAge = user?.childAge?.toString() ?: ""
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载用户信息失败"
                )
            }
        }
    }

    fun updateNickname(nickname: String) {
        _uiState.value = _uiState.value.copy(nickname = nickname)
    }

    fun updateChildName(childName: String) {
        _uiState.value = _uiState.value.copy(childName = childName)
    }

    fun updateChildGender(gender: String) {
        _uiState.value = _uiState.value.copy(childGender = gender)
    }

    fun updateChildAge(age: String) {
        _uiState.value = _uiState.value.copy(childAge = age)
    }

    fun saveProfile() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")

            try {
                val currentState = _uiState.value

                // 验证输入
                if (currentState.nickname.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入昵称"
                    )
                    return@launch
                }

                if (currentState.childName.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入孩子姓名"
                    )
                    return@launch
                }

                if (currentState.childGender.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请选择孩子性别"
                    )
                    return@launch
                }

                if (currentState.childAge.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入孩子年龄"
                    )
                    return@launch
                }

                try {
                    currentState.childAge.toInt()
                } catch (e: NumberFormatException) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入有效的年龄"
                    )
                    return@launch
                }

                // TODO: 调用实际的API保存用户信息
                // authRepository.updateProfile(...)

                // 模拟保存过程
                kotlinx.coroutines.delay(1000)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    saveSuccess = true
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "保存失败"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
}
