package com.example.child.ui.screens.main

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.child.R
import com.example.child.ui.screens.home.HomeScreen
import com.example.child.ui.screens.monthly.MonthlyScreen
import com.example.child.ui.screens.assistant.AssistantScreen
import com.example.child.ui.screens.profile.ProfileScreen
import com.example.child.ui.screens.profile.EditProfileScreen
import com.example.child.ui.screens.profile.PasswordScreen
import com.example.child.ui.screens.profile.ChangePasswordScreen
import com.example.child.ui.screens.profile.BindPhoneScreen
import com.example.child.ui.screens.detail.HourlyDetailScreen
import com.example.child.ui.screens.device.DeviceManagementScreen
import com.example.child.ui.screens.about.AboutUsScreen
import com.example.child.ui.screens.notification.NotificationSettingsScreen
import com.example.child.ui.theme.*

// 底部导航项定义
sealed class BottomNavItem(
    val route: String,
    val icon: ImageVector,
    val label: String
) {
    object Home : BottomNavItem("home", Icons.Default.Home, "首页")
    object Monthly : BottomNavItem("monthly", Icons.Default.DateRange, "月报")
    object Assistant : BottomNavItem("assistant", Icons.Default.Info, "助手")
    object Profile : BottomNavItem("profile", Icons.Default.Person, "我的")
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    val bottomNavItems = listOf(
        BottomNavItem.Home,
        BottomNavItem.Monthly,
        BottomNavItem.Assistant,
        BottomNavItem.Profile
    )

    // 沉浸式布局：使用 Box 而不是 Scaffold
    Box(modifier = Modifier.fillMaxSize()) {
        // 内容区域 - 填充整个屏幕
        NavHost(
            navController = navController,
            startDestination = BottomNavItem.Home.route,
            modifier = Modifier.fillMaxSize()
        ) {
            composable(BottomNavItem.Home.route) {
                HomeScreen(
                    onNavigateToDetail = { timeSlot ->
                        navController.navigate("detail/$timeSlot")
                    }
                )
            }
            composable(BottomNavItem.Monthly.route) {
                MonthlyScreen(
                    onNavigateToHome = { date ->
                        // 导航到首页并传递日期参数
                        navController.navigate("${BottomNavItem.Home.route}?date=$date") {
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    }
                )
            }
            composable(BottomNavItem.Assistant.route) {
                AssistantScreen()
            }
            composable(BottomNavItem.Profile.route) {
                ProfileScreen(
                    onNavigateToEditProfile = {
                        navController.navigate("edit_profile")
                    },
                    onNavigateToChangePassword = {
                        navController.navigate("password")
                    },
                    onNavigateToDeviceManagement = {
                        navController.navigate("device_management")
                    },
                    onNavigateToAboutUs = {
                        navController.navigate("about_us")
                    },
                    onNavigateToNotificationSettings = {
                        navController.navigate("notification_settings")
                    }
                )
            }
            composable("password") {
                PasswordScreen(
                    onNavigateToChangePassword = {
                        navController.navigate("change_password")
                    },
                    onNavigateToBindPhone = {
                        navController.navigate("bind_phone")
                    },
                    onBackClick = {
                        navController.popBackStack()
                    },
                    onLogout = {
                        // 退出登录，返回到登录页面
                        navController.navigate("login") {
                            popUpTo(0) { inclusive = true }
                        }
                    }
                )
            }
            composable("edit_profile") {
                EditProfileScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("change_password") {
                ChangePasswordScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("bind_phone") {
                BindPhoneScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("detail/{timeSlot}") { backStackEntry ->
                val timeSlot = backStackEntry.arguments?.getString("timeSlot") ?: "08:00"
                HourlyDetailScreen(
                    timeSlot = timeSlot,
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("device_management") {
                DeviceManagementScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("about_us") {
                AboutUsScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
            composable("notification_settings") {
                NotificationSettingsScreen(
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            }
        }

        // 底部导航栏 - 浮动在内容之上
        NavigationBar(
            containerColor = Surface.copy(alpha = 0.95f), // 半透明效果
            contentColor = Primary,
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
        ) {
            bottomNavItems.forEach { item ->
                val selected = currentDestination?.hierarchy?.any { it.route == item.route } == true

                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.label
                        )
                    },
                    label = {
                        Text(
                            text = item.label,
                            style = MaterialTheme.typography.labelSmall
                        )
                    },
                    selected = selected,
                    onClick = {
                        navController.navigate(item.route) {
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    },
                    colors = NavigationBarItemDefaults.colors(
                        selectedIconColor = Primary,
                        selectedTextColor = Primary,
                        unselectedIconColor = TextSecondary,
                        unselectedTextColor = TextSecondary,
                        indicatorColor = Primary.copy(alpha = 0.1f)
                    )
                )
            }
        }
    }
}
