package com.example.child.ui.screens.notification

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.NotificationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class NotificationSettingsUiState(
    val hasNotificationPermission: Boolean = false,
    val notificationsEnabled: Boolean = false,
    val growthReminders: Boolean = false,
    val analysisNotifications: Boolean = false,
    val dailySummary: Boolean = false,
    val weeklyReport: Boolean = false,
    val isLoading: Boolean = false
)

@HiltViewModel
class NotificationSettingsViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(NotificationSettingsUiState())
    val uiState: StateFlow<NotificationSettingsUiState> = _uiState.asStateFlow()
    
    init {
        loadSettings()
    }
    
    fun checkNotificationPermission(context: Context) {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
        
        _uiState.value = _uiState.value.copy(hasNotificationPermission = hasPermission)
        
        if (hasPermission) {
            loadSettings()
        }
    }
    
    fun updatePermissionStatus(granted: Boolean) {
        _uiState.value = _uiState.value.copy(hasNotificationPermission = granted)
        if (granted) {
            loadSettings()
        }
    }
    
    private fun loadSettings() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val settings = notificationRepository.getNotificationSettings()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    notificationsEnabled = settings.notificationsEnabled,
                    growthReminders = settings.growthReminders,
                    analysisNotifications = settings.analysisNotifications,
                    dailySummary = settings.dailySummary,
                    weeklyReport = settings.weeklyReport
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }
    
    fun toggleNotifications(enabled: Boolean) {
        viewModelScope.launch {
            try {
                notificationRepository.setNotificationsEnabled(enabled)
                _uiState.value = _uiState.value.copy(notificationsEnabled = enabled)
                
                // 如果关闭总开关，同时关闭所有子选项
                if (!enabled) {
                    _uiState.value = _uiState.value.copy(
                        growthReminders = false,
                        analysisNotifications = false,
                        dailySummary = false,
                        weeklyReport = false
                    )
                    notificationRepository.setGrowthReminders(false)
                    notificationRepository.setAnalysisNotifications(false)
                    notificationRepository.setDailySummary(false)
                    notificationRepository.setWeeklyReport(false)
                }
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun toggleGrowthReminders(enabled: Boolean) {
        viewModelScope.launch {
            try {
                notificationRepository.setGrowthReminders(enabled)
                _uiState.value = _uiState.value.copy(growthReminders = enabled)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun toggleAnalysisNotifications(enabled: Boolean) {
        viewModelScope.launch {
            try {
                notificationRepository.setAnalysisNotifications(enabled)
                _uiState.value = _uiState.value.copy(analysisNotifications = enabled)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun toggleDailySummary(enabled: Boolean) {
        viewModelScope.launch {
            try {
                notificationRepository.setDailySummary(enabled)
                _uiState.value = _uiState.value.copy(dailySummary = enabled)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun toggleWeeklyReport(enabled: Boolean) {
        viewModelScope.launch {
            try {
                notificationRepository.setWeeklyReport(enabled)
                _uiState.value = _uiState.value.copy(weeklyReport = enabled)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
}
