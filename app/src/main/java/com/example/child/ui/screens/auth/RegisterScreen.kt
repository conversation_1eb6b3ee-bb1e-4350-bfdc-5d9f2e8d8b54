package com.example.child.ui.screens.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.example.child.R
import com.example.child.ui.components.*
import com.example.child.ui.theme.*

@Composable
fun RegisterScreen(
    navController: NavController,
    viewModel: RegisterViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(uiState.navigationTarget) {
        uiState.navigationTarget?.let { target ->
            navController.navigate(target) {
                popUpTo("register") { inclusive = true }
            }
        }
    }
    
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // 背景图片（固定不滚动）
        Image(
            painter = painterResource(id = R.drawable.ic_launcher_background),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        
        // 渐变遮罩
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            GradientStart.copy(alpha = 0.8f),
                            GradientEnd.copy(alpha = 0.9f)
                        )
                    )
                )
        )
        
        // 可滚动内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp))
            
            // 标题
            Text(
                text = stringResource(id = R.string.complete_profile),
                color = OnPrimary,
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = stringResource(id = R.string.profile_hint),
                color = OnPrimary.copy(alpha = 0.8f),
                fontSize = 14.sp
            )
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // 注册表单
            RoundedCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 用户姓名
                CustomTextField(
                    value = uiState.userName,
                    onValueChange = viewModel::updateUserName,
                    label = stringResource(id = R.string.user_name),
                    placeholder = "请输入您的姓名",
                    isError = uiState.userNameError.isNotEmpty(),
                    errorMessage = uiState.userNameError
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 孩子姓名
                CustomTextField(
                    value = uiState.childName,
                    onValueChange = viewModel::updateChildName,
                    label = stringResource(id = R.string.child_name),
                    placeholder = "请输入孩子的姓名",
                    isError = uiState.childNameError.isNotEmpty(),
                    errorMessage = uiState.childNameError
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 孩子年龄
                CustomTextField(
                    value = uiState.childAge,
                    onValueChange = viewModel::updateChildAge,
                    label = stringResource(id = R.string.child_age),
                    placeholder = "请输入孩子的年龄",
                    isError = uiState.childAgeError.isNotEmpty(),
                    errorMessage = uiState.childAgeError
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 孩子性别
                Text(
                    text = stringResource(id = R.string.child_gender),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = TextPrimary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .weight(1f)
                            .selectable(
                                selected = uiState.childGender == "male",
                                onClick = { viewModel.updateChildGender("male") }
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = uiState.childGender == "male",
                            onClick = { viewModel.updateChildGender("male") },
                            colors = RadioButtonDefaults.colors(selectedColor = Primary)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = stringResource(id = R.string.boy),
                            fontSize = 14.sp,
                            color = TextPrimary
                        )
                    }
                    
                    Row(
                        modifier = Modifier
                            .weight(1f)
                            .selectable(
                                selected = uiState.childGender == "female",
                                onClick = { viewModel.updateChildGender("female") }
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = uiState.childGender == "female",
                            onClick = { viewModel.updateChildGender("female") },
                            colors = RadioButtonDefaults.colors(selectedColor = Primary)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = stringResource(id = R.string.girl),
                            fontSize = 14.sp,
                            color = TextPrimary
                        )
                    }
                }
                
                if (uiState.childGenderError.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = uiState.childGenderError,
                        color = Error,
                        fontSize = 12.sp
                    )
                }
                
                Spacer(modifier = Modifier.height(32.dp))
                
                // 完成注册按钮
                GradientButton(
                    text = stringResource(id = R.string.register),
                    onClick = viewModel::register,
                    enabled = !uiState.isLoading
                )
                
                if (uiState.isLoading) {
                    Spacer(modifier = Modifier.height(16.dp))
                    LoadingIndicator(message = "注册中...")
                }
                
                if (uiState.error.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = uiState.error,
                        color = Error,
                        fontSize = 14.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(40.dp))
        }
    }
}
