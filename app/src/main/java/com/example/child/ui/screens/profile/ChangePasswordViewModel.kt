package com.example.child.ui.screens.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.child.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class ChangePasswordUiState(
    val isLoading: Boolean = false,
    val oldPassword: String = "",
    val newPassword: String = "",
    val confirmPassword: String = "",
    val error: String = "",
    val changeSuccess: Boolean = false
)

@HiltViewModel
class ChangePasswordViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ChangePasswordUiState())
    val uiState: StateFlow<ChangePasswordUiState> = _uiState.asStateFlow()
    
    fun updateOldPassword(password: String) {
        _uiState.value = _uiState.value.copy(oldPassword = password)
    }
    
    fun updateNewPassword(password: String) {
        _uiState.value = _uiState.value.copy(newPassword = password)
    }
    
    fun updateConfirmPassword(password: String) {
        _uiState.value = _uiState.value.copy(confirmPassword = password)
    }
    
    fun changePassword() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = "")
            
            try {
                val currentState = _uiState.value
                
                // 验证输入
                if (currentState.oldPassword.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入原密码"
                    )
                    return@launch
                }
                
                if (currentState.newPassword.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请输入新密码"
                    )
                    return@launch
                }
                
                if (currentState.newPassword.length < 6) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "新密码长度不能少于6位"
                    )
                    return@launch
                }
                
                if (currentState.confirmPassword.isBlank()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "请确认新密码"
                    )
                    return@launch
                }
                
                if (currentState.newPassword != currentState.confirmPassword) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "两次输入的密码不一致"
                    )
                    return@launch
                }
                
                if (currentState.oldPassword == currentState.newPassword) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "新密码不能与原密码相同"
                    )
                    return@launch
                }
                
                // TODO: 调用实际的API修改密码
                // authRepository.changePassword(currentState.oldPassword, currentState.newPassword)
                
                // 模拟修改过程
                kotlinx.coroutines.delay(1000)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    changeSuccess = true
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "修改密码失败"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = "")
    }
}
