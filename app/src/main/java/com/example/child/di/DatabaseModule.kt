package com.example.child.di

import android.content.Context
import androidx.room.Room
import com.example.child.data.database.AppDatabase
import com.example.child.data.database.dao.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            AppDatabase::class.java,
            "child_growth_database"
        ).build()
    }
    
    @Provides
    fun provideUserDao(database: AppDatabase): UserDao = database.userDao()
    
    @Provides
    fun provideAudioRecordDao(database: AppDatabase): AudioRecordDao = database.audioRecordDao()
    
    @Provides
    fun provideDailyAnalysisDao(database: AppDatabase): DailyAnalysisDao = database.dailyAnalysisDao()
    
    @Provides
    fun provideMonthlyReportDao(database: AppDatabase): MonthlyReportDao = database.monthlyReportDao()
}
