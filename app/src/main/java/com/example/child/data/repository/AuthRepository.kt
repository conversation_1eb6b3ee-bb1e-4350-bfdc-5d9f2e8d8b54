package com.example.child.data.repository

import com.example.child.data.database.dao.UserDao
import com.example.child.data.model.User
import com.example.child.data.network.ApiService
import com.example.child.data.network.dto.*
import com.example.child.utils.Result
import com.example.child.utils.SharedPreferencesManager
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val apiService: ApiService,
    private val userDao: UserDao,
    private val sharedPreferencesManager: SharedPreferencesManager
) {
    
    fun getCurrentUser(): Flow<User?> = userDao.getCurrentUser()
    
    fun isLoggedIn(): Boolean = sharedPreferencesManager.getToken() != null
    
    suspend fun sendSmsCode(phone: String): Result<Unit> {
        return try {
            val response = apiService.sendSmsCode(SendSmsRequest(phone))
            if (response.isSuccessful && response.body()?.success == true) {
                Result.Success(Unit)
            } else {
                Result.Error(response.body()?.message ?: "发送验证码失败")
            }
        } catch (e: Exception) {
            Result.Error(e.message ?: "网络错误")
        }
    }
    
    suspend fun verifySmsCode(phone: String, code: String): Result<LoginResponse> {
        return try {
            val response = apiService.verifySmsCode(VerifySmsRequest(phone, code))
            if (response.isSuccessful && response.body()?.success == true) {
                val loginResponse = response.body()!!.data!!
                
                // 保存token
                sharedPreferencesManager.saveToken(loginResponse.token)
                
                // 保存用户信息到本地数据库
                val user = User(
                    id = loginResponse.user.id,
                    phone = loginResponse.user.phone,
                    name = loginResponse.user.name,
                    avatar = loginResponse.user.avatar,
                    childName = loginResponse.user.childName,
                    childAge = loginResponse.user.childAge,
                    childGender = loginResponse.user.childGender,
                    createdAt = loginResponse.user.createdAt,
                    updatedAt = loginResponse.user.updatedAt
                )
                userDao.insertUser(user)
                
                Result.Success(loginResponse)
            } else {
                Result.Error(response.body()?.message ?: "验证码错误")
            }
        } catch (e: Exception) {
            Result.Error(e.message ?: "网络错误")
        }
    }
    
    suspend fun register(
        phone: String,
        name: String,
        childName: String,
        childAge: Int,
        childGender: String
    ): Result<LoginResponse> {
        return try {
            val request = RegisterRequest(phone, name, childName, childAge, childGender)
            val response = apiService.register(request)
            if (response.isSuccessful && response.body()?.success == true) {
                val loginResponse = response.body()!!.data!!
                
                // 保存token
                sharedPreferencesManager.saveToken(loginResponse.token)
                
                // 保存用户信息到本地数据库
                val user = User(
                    id = loginResponse.user.id,
                    phone = loginResponse.user.phone,
                    name = loginResponse.user.name,
                    avatar = loginResponse.user.avatar,
                    childName = loginResponse.user.childName,
                    childAge = loginResponse.user.childAge,
                    childGender = loginResponse.user.childGender,
                    createdAt = loginResponse.user.createdAt,
                    updatedAt = loginResponse.user.updatedAt
                )
                userDao.insertUser(user)
                
                Result.Success(loginResponse)
            } else {
                Result.Error(response.body()?.message ?: "注册失败")
            }
        } catch (e: Exception) {
            Result.Error(e.message ?: "网络错误")
        }
    }
    
    suspend fun logout(): Result<Unit> {
        return try {
            val token = sharedPreferencesManager.getToken()
            if (token != null) {
                apiService.logout("Bearer $token")
            }
            
            // 清除本地数据
            sharedPreferencesManager.clearToken()
            userDao.deleteAllUsers()
            
            Result.Success(Unit)
        } catch (e: Exception) {
            // 即使网络请求失败，也要清除本地数据
            sharedPreferencesManager.clearToken()
            userDao.deleteAllUsers()
            Result.Success(Unit)
        }
    }
}
