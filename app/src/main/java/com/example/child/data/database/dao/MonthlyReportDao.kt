package com.example.child.data.database.dao

import androidx.room.*
import com.example.child.data.model.MonthlyReport
import kotlinx.coroutines.flow.Flow

@Dao
interface MonthlyReportDao {
    
    @Query("SELECT * FROM monthly_reports WHERE userId = :userId ORDER BY year DESC, month DESC")
    fun getMonthlyReportsByUser(userId: String): Flow<List<MonthlyReport>>
    
    @Query("SELECT * FROM monthly_reports WHERE userId = :userId AND year = :year AND month = :month")
    suspend fun getMonthlyReport(userId: String, year: Int, month: Int): MonthlyReport?
    
    @Query("SELECT * FROM monthly_reports WHERE id = :id")
    suspend fun getMonthlyReportById(id: String): MonthlyReport?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMonthlyReport(monthlyReport: MonthlyReport)
    
    @Update
    suspend fun updateMonthlyReport(monthlyReport: MonthlyReport)
    
    @Delete
    suspend fun deleteMonthlyReport(monthlyReport: MonthlyReport)
    
    @Query("DELETE FROM monthly_reports WHERE userId = :userId")
    suspend fun deleteMonthlyReportsByUser(userId: String)
}
