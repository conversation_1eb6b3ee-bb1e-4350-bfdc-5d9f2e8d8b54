package com.example.child.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.example.child.data.model.*
import com.example.child.data.database.dao.*

@Database(
    entities = [
        User::class,
        AudioRecord::class,
        DailyAnalysis::class,
        MonthlyReport::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun userDao(): UserDao
    abstract fun audioRecordDao(): AudioRecordDao
    abstract fun dailyAnalysisDao(): DailyAnalysisDao
    abstract fun monthlyReportDao(): MonthlyReportDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "child_growth_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
