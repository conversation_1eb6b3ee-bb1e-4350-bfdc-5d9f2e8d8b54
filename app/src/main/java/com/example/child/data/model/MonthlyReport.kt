package com.example.child.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "monthly_reports")
@Parcelize
data class MonthlyReport(
    @PrimaryKey
    val id: String,
    val userId: String,
    val year: Int,
    val month: Int, // 1-12
    val summary: String? = null, // 月度总结
    val keyMilestones: List<String> = emptyList(), // 关键里程碑
    val averageHappiness: Float? = null, // 平均快乐指数
    val averageActivity: Float? = null, // 平均活跃度
    val totalAudioHours: Float? = null, // 总音频时长（小时）
    val growthAreas: List<String> = emptyList(), // 成长领域
    val recommendations: List<String> = emptyList(), // 建议
    val dailyAnalysisIds: List<String> = emptyList(), // 关联的日分析ID
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable
