package com.example.child.data.database.dao

import androidx.room.*
import com.example.child.data.model.DailyAnalysis
import kotlinx.coroutines.flow.Flow

@Dao
interface DailyAnalysisDao {
    
    @Query("SELECT * FROM daily_analysis WHERE userId = :userId ORDER BY date DESC")
    fun getDailyAnalysisByUser(userId: String): Flow<List<DailyAnalysis>>
    
    @Query("SELECT * FROM daily_analysis WHERE userId = :userId AND date = :date")
    suspend fun getDailyAnalysisByDate(userId: String, date: String): DailyAnalysis?
    
    @Query("SELECT * FROM daily_analysis WHERE userId = :userId AND date LIKE :yearMonth || '%' ORDER BY date ASC")
    suspend fun getDailyAnalysisByMonth(userId: String, yearMonth: String): List<DailyAnalysis>
    
    @Query("SELECT * FROM daily_analysis WHERE id = :id")
    suspend fun getDailyAnalysisById(id: String): DailyAnalysis?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDailyAnalysis(dailyAnalysis: DailyAnalysis)
    
    @Update
    suspend fun updateDailyAnalysis(dailyAnalysis: DailyAnalysis)
    
    @Delete
    suspend fun deleteDailyAnalysis(dailyAnalysis: DailyAnalysis)
    
    @Query("DELETE FROM daily_analysis WHERE userId = :userId")
    suspend fun deleteDailyAnalysisByUser(userId: String)
}
