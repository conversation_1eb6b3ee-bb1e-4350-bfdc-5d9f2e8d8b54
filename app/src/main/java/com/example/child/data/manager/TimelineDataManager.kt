package com.example.child.data.manager

import androidx.compose.ui.graphics.Color
import com.example.child.ui.screens.home.TimelineItem
import com.example.child.ui.screens.detail.ActivityDetail
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TimelineDataManager @Inject constructor() {
    
    // 模拟获取指定日期的时间轴数据
    fun getTimelineData(date: String): Flow<List<TimelineItem>> = flow {
        // 模拟网络延迟
        delay(500)
        
        val timelineItems = when (date) {
            "2024年1月15日" -> getJan15Data()
            "2024年1月16日" -> getJan16Data()
            else -> getDefaultData()
        }
        
        emit(timelineItems)
    }
    
    // 模拟获取指定时间段的详细数据
    fun getHourlyDetails(timeSlot: String, date: String = "2024年1月15日"): Flow<List<ActivityDetail>> = flow {
        delay(300)
        
        val details = when (timeSlot) {
            "08:00" -> get8AMDetails()
            "10:00" -> get10AMDetails()
            "14:00" -> get2PMDetails()
            else -> getDefaultDetails(timeSlot)
        }
        
        emit(details)
    }
    
    // 模拟获取统计数据
    fun getStatisticsData(date: String): Flow<Triple<String, String, String>> = flow {
        delay(200)
        
        val stats = when (date) {
            "2024年1月15日" -> Triple("8小时", "6小时", "2小时")
            "2024年1月16日" -> Triple("7小时", "7小时", "3小时")
            else -> Triple("8小时", "8小时", "8小时")
        }
        
        emit(stats)
    }
    
    private fun getJan15Data(): List<TimelineItem> = listOf(
        TimelineItem(
            time = "08:00",
            status = "早餐时间",
            description = "享用了牛奶、面包和水果，营养均衡的早餐开启美好的一天！",
            summary = "食欲良好，心情愉悦。建议：继续保持规律作息，培养健康的饮食习惯。",
            iconColor = Color(0xFFFF6B6B)
        ),
        TimelineItem(
            time = "10:00",
            status = "学习时间",
            description = "专注学习数学，完成了加减法练习，表现出很好的学习兴趣。",
            summary = "学习专注度高，理解能力强。建议：适当增加趣味性练习，保持学习热情。",
            iconColor = Color(0xFF4ECDC4)
        ),
        TimelineItem(
            time = "14:00",
            status = "运动时间",
            description = "在户外进行了30分钟的跑步和游戏，活力满满！",
            summary = "运动积极性高，体能表现良好。建议：继续保持户外运动习惯。",
            iconColor = Color(0xFF45B7D1)
        )
    )
    
    private fun getJan16Data(): List<TimelineItem> = listOf(
        TimelineItem(
            time = "07:30",
            status = "起床时间",
            description = "今天起床比较早，精神状态很好，主动完成洗漱。",
            summary = "作息规律，自理能力强。建议：保持早睡早起的好习惯。",
            iconColor = Color(0xFFFFB74D)
        ),
        TimelineItem(
            time = "09:00",
            status = "阅读时间",
            description = "阅读了喜欢的故事书，能够复述故事内容，表现出很好的理解能力。",
            summary = "阅读兴趣浓厚，理解能力优秀。建议：增加阅读时间，培养阅读习惯。",
            iconColor = Color(0xFF81C784)
        ),
        TimelineItem(
            time = "15:30",
            status = "创作时间",
            description = "用彩笔画画，创作了一幅关于家庭的画作，想象力丰富。",
            summary = "创造力强，艺术天赋突出。建议：提供更多艺术创作机会。",
            iconColor = Color(0xFFBA68C8)
        )
    )
    
    private fun getDefaultData(): List<TimelineItem> = listOf(
        TimelineItem(
            time = "08:00",
            status = "日常活动",
            description = "正常的日常活动安排",
            summary = "一切正常，继续保持。",
            iconColor = Color(0xFF90A4AE)
        )
    )
    
    private fun get8AMDetails(): List<ActivityDetail> = listOf(
        ActivityDetail(
            time = "08:00-08:15",
            activity = "起床洗漱",
            description = "孩子主动起床，情绪良好，洗漱过程中表现出很好的自理能力",
            emotion = "愉悦",
            recommendation = "继续保持良好的作息习惯"
        ),
        ActivityDetail(
            time = "08:15-08:30",
            activity = "早餐时间",
            description = "享用牛奶、面包和水果，食欲良好，用餐礼仪表现优秀",
            emotion = "满足",
            recommendation = "可以尝试增加一些新的健康食物"
        ),
        ActivityDetail(
            time = "08:30-08:45",
            activity = "准备上学",
            description = "整理书包，检查作业，表现出很好的责任心和组织能力",
            emotion = "专注",
            recommendation = "鼓励孩子继续保持这种自主性"
        ),
        ActivityDetail(
            time = "08:45-09:00",
            activity = "出门上学",
            description = "与家长告别，情绪稳定，对新一天的学习充满期待",
            emotion = "期待",
            recommendation = "保持积极的学习态度"
        )
    )
    
    private fun get10AMDetails(): List<ActivityDetail> = listOf(
        ActivityDetail(
            time = "10:00-10:20",
            activity = "数学练习",
            description = "专注完成加减法练习，正确率很高，遇到困难时会主动思考",
            emotion = "专注",
            recommendation = "可以适当增加题目难度，挑战思维能力"
        ),
        ActivityDetail(
            time = "10:20-10:40",
            activity = "课间休息",
            description = "与同学友好互动，分享学习用品，表现出良好的社交能力",
            emotion = "开心",
            recommendation = "鼓励更多的同伴交流，培养团队合作精神"
        ),
        ActivityDetail(
            time = "10:40-11:00",
            activity = "语文学习",
            description = "认真听讲，积极回答问题，字迹工整，学习态度端正",
            emotion = "认真",
            recommendation = "保持学习热情，可以增加课外阅读"
        )
    )
    
    private fun get2PMDetails(): List<ActivityDetail> = listOf(
        ActivityDetail(
            time = "14:00-14:20",
            activity = "户外跑步",
            description = "在操场上跑步，速度适中，呼吸均匀，体能表现良好",
            emotion = "兴奋",
            recommendation = "继续保持运动习惯，可以尝试不同的运动项目"
        ),
        ActivityDetail(
            time = "14:20-14:40",
            activity = "团体游戏",
            description = "参与集体游戏，遵守规则，与其他小朋友配合默契",
            emotion = "快乐",
            recommendation = "多参与团体活动，培养合作精神和领导能力"
        ),
        ActivityDetail(
            time = "14:40-15:00",
            activity = "放松休息",
            description = "运动后适当休息，主动补充水分，懂得照顾自己",
            emotion = "放松",
            recommendation = "培养运动后的恢复意识，养成健康的运动习惯"
        )
    )
    
    private fun getDefaultDetails(timeSlot: String): List<ActivityDetail> = listOf(
        ActivityDetail(
            time = "$timeSlot-${getNextHour(timeSlot)}",
            activity = "常规活动",
            description = "进行日常的学习和生活活动",
            emotion = "平静",
            recommendation = "保持规律的作息安排"
        )
    )
    
    private fun getNextHour(timeSlot: String): String {
        val hour = timeSlot.substring(0, 2).toInt()
        val nextHour = (hour + 1).toString().padStart(2, '0')
        return "$nextHour:00"
    }
}
