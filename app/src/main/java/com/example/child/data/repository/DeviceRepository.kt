package com.example.child.data.repository

import android.content.Context
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.Environment
import android.os.StatFs
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

data class DeviceInfo(
    val name: String,
    val totalSpace: Long,
    val usedSpace: Long,
    val availableSpace: Long
)

@Singleton
class DeviceRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val usbManager: UsbManager by lazy {
        context.getSystemService(Context.USB_SERVICE) as UsbManager
    }
    
    suspend fun getConnectedDeviceInfo(): DeviceInfo? = withContext(Dispatchers.IO) {
        try {
            // 检查USB设备
            val usbDevices = usbManager.deviceList
            if (usbDevices.isNotEmpty()) {
                val device = usbDevices.values.first()
                return@withContext getUsbDeviceInfo(device)
            }
            
            // 如果没有USB设备，检查外部存储
            val externalStorageInfo = getExternalStorageInfo()
            if (externalStorageInfo != null) {
                return@withContext externalStorageInfo
            }
            
            null
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getUsbDeviceInfo(device: UsbDevice): DeviceInfo? {
        return try {
            // 尝试获取USB设备的存储信息
            // 这里使用模拟数据，实际实现需要根据具体的USB设备API
            val deviceName = device.deviceName ?: "USB设备"
            
            // 模拟数据 - 实际应用中需要通过USB API获取真实数据
            val totalSpace = 32L * 1024 * 1024 * 1024 // 32GB
            val usedSpace = (totalSpace * 0.6).toLong() // 60% 已使用
            val availableSpace = totalSpace - usedSpace
            
            DeviceInfo(
                name = deviceName,
                totalSpace = totalSpace,
                usedSpace = usedSpace,
                availableSpace = availableSpace
            )
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getExternalStorageInfo(): DeviceInfo? {
        return try {
            val externalStorageDir = Environment.getExternalStorageDirectory()
            if (externalStorageDir != null && externalStorageDir.exists()) {
                val stat = StatFs(externalStorageDir.path)
                val totalSpace = stat.totalBytes
                val availableSpace = stat.availableBytes
                val usedSpace = totalSpace - availableSpace
                
                DeviceInfo(
                    name = "外部存储",
                    totalSpace = totalSpace,
                    usedSpace = usedSpace,
                    availableSpace = availableSpace
                )
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun clearDevice(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 检查是否有USB设备连接
            val usbDevices = usbManager.deviceList
            if (usbDevices.isNotEmpty()) {
                return@withContext clearUsbDevice()
            }
            
            // 如果没有USB设备，清理应用相关的外部存储数据
            return@withContext clearExternalStorageData()
        } catch (e: Exception) {
            false
        }
    }
    
    private suspend fun clearUsbDevice(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 实际实现中需要通过USB API格式化设备
            // 这里使用模拟实现
            kotlinx.coroutines.delay(2000) // 模拟格式化过程
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private suspend fun clearExternalStorageData(): Boolean = withContext(Dispatchers.IO) {
        try {
            // 清理应用在外部存储中的数据
            val appExternalDir = context.getExternalFilesDir(null)
            if (appExternalDir != null && appExternalDir.exists()) {
                deleteRecursively(appExternalDir)
            }
            
            // 清理缓存目录
            val cacheDir = context.externalCacheDir
            if (cacheDir != null && cacheDir.exists()) {
                deleteRecursively(cacheDir)
            }
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun deleteRecursively(file: File): Boolean {
        return try {
            if (file.isDirectory) {
                file.listFiles()?.forEach { child ->
                    deleteRecursively(child)
                }
            }
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    fun isDeviceConnected(): Boolean {
        return try {
            val usbDevices = usbManager.deviceList
            usbDevices.isNotEmpty() || Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
        } catch (e: Exception) {
            false
        }
    }
}
