package com.example.child.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "daily_analysis")
@Parcelize
data class DailyAnalysis(
    @PrimaryKey
    val id: String,
    val userId: String,
    val date: String, // YYYY-MM-DD
    val overallMood: String? = null, // 整体情绪
    val learningProgress: String? = null, // 学习进展
    val behaviorAnalysis: String? = null, // 行为分析
    val growthHighlights: String? = null, // 成长亮点
    val suggestions: String? = null, // 建议
    val happinessLevel: Int? = null, // 快乐指数 1-10
    val activityLevel: Int? = null, // 活跃度 1-10
    val socialInteraction: String? = null, // 社交互动
    val audioRecordIds: List<String> = emptyList(), // 关联的音频记录ID
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable
