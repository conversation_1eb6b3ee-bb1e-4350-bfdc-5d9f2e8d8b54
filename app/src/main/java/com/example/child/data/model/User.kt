package com.example.child.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "users")
@Parcelize
data class User(
    @PrimaryKey
    val id: String,
    val phone: String,
    val name: String? = null,
    val avatar: String? = null,
    val childName: String? = null,
    val childAge: Int? = null,
    val childGender: String? = null, // "male" or "female"
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable
