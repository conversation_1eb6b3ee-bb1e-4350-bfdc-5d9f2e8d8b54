package com.example.child.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Entity(tableName = "audio_records")
@Parcelize
data class AudioRecord(
    @PrimaryKey
    val id: String,
    val userId: String,
    val filePath: String,
    val fileName: String,
    val duration: Long, // 时长（毫秒）
    val fileSize: Long, // 文件大小（字节）
    val recordDate: String, // 录制日期 YYYY-MM-DD
    val recordHour: Int, // 录制小时 0-23
    val transcription: String? = null, // 语音转文字结果
    val analysis: String? = null, // AI分析结果
    val isProcessed: Boolean = false, // 是否已处理
    val uploadStatus: String = "pending", // "pending", "uploading", "completed", "failed"
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable
