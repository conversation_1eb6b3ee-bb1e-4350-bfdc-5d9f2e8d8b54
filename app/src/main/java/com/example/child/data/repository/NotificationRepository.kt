package com.example.child.data.repository

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.child.R
import com.example.child.utils.SharedPreferencesManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

data class NotificationSettings(
    val notificationsEnabled: Boolean = true,
    val growthReminders: <PERSON>olean = true,
    val analysisNotifications: Boolean = true,
    val dailySummary: <PERSON>olean = true,
    val weeklyReport: Boolean = true
)

@Singleton
class NotificationRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val sharedPreferencesManager: SharedPreferencesManager
) {

    companion object {
        private const val CHANNEL_ID_GROWTH = "growth_reminders"
        private const val CHANNEL_ID_ANALYSIS = "analysis_notifications"
        private const val CHANNEL_ID_SUMMARY = "daily_summary"
        private const val CHANNEL_ID_REPORT = "weekly_report"

        private const val PREF_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val PREF_GROWTH_REMINDERS = "growth_reminders"
        private const val PREF_ANALYSIS_NOTIFICATIONS = "analysis_notifications"
        private const val PREF_DAILY_SUMMARY = "daily_summary"
        private const val PREF_WEEKLY_REPORT = "weekly_report"
    }

    init {
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 成长提醒通道
            val growthChannel = NotificationChannel(
                CHANNEL_ID_GROWTH,
                "成长提醒",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "孩子成长关键节点提醒"
            }

            // 分析完成通道
            val analysisChannel = NotificationChannel(
                CHANNEL_ID_ANALYSIS,
                "分析完成",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "语音分析完成通知"
            }

            // 每日总结通道
            val summaryChannel = NotificationChannel(
                CHANNEL_ID_SUMMARY,
                "每日总结",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "每日成长总结推送"
            }

            // 每周报告通道
            val reportChannel = NotificationChannel(
                CHANNEL_ID_REPORT,
                "每周报告",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "每周成长报告提醒"
            }

            notificationManager.createNotificationChannels(
                listOf(growthChannel, analysisChannel, summaryChannel, reportChannel)
            )
        }
    }

    suspend fun getNotificationSettings(): NotificationSettings = withContext(Dispatchers.IO) {
        NotificationSettings(
            notificationsEnabled = sharedPreferencesManager.getBoolean(PREF_NOTIFICATIONS_ENABLED, true),
            growthReminders = sharedPreferencesManager.getBoolean(PREF_GROWTH_REMINDERS, true),
            analysisNotifications = sharedPreferencesManager.getBoolean(PREF_ANALYSIS_NOTIFICATIONS, true),
            dailySummary = sharedPreferencesManager.getBoolean(PREF_DAILY_SUMMARY, true),
            weeklyReport = sharedPreferencesManager.getBoolean(PREF_WEEKLY_REPORT, true)
        )
    }

    suspend fun setNotificationsEnabled(enabled: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferencesManager.saveBoolean(PREF_NOTIFICATIONS_ENABLED, enabled)
    }

    suspend fun setGrowthReminders(enabled: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferencesManager.saveBoolean(PREF_GROWTH_REMINDERS, enabled)
    }

    suspend fun setAnalysisNotifications(enabled: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferencesManager.saveBoolean(PREF_ANALYSIS_NOTIFICATIONS, enabled)
    }

    suspend fun setDailySummary(enabled: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferencesManager.saveBoolean(PREF_DAILY_SUMMARY, enabled)
    }

    suspend fun setWeeklyReport(enabled: Boolean) = withContext(Dispatchers.IO) {
        sharedPreferencesManager.saveBoolean(PREF_WEEKLY_REPORT, enabled)
    }

    suspend fun sendGrowthReminder(title: String, content: String) {
        if (!isNotificationTypeEnabled(PREF_GROWTH_REMINDERS)) return

        val notification = NotificationCompat.Builder(context, CHANNEL_ID_GROWTH)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()

        NotificationManagerCompat.from(context).notify(1001, notification)
    }

    suspend fun sendAnalysisNotification(title: String, content: String) {
        if (!isNotificationTypeEnabled(PREF_ANALYSIS_NOTIFICATIONS)) return

        val notification = NotificationCompat.Builder(context, CHANNEL_ID_ANALYSIS)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()

        NotificationManagerCompat.from(context).notify(1002, notification)
    }

    suspend fun sendDailySummary(title: String, content: String) {
        if (!isNotificationTypeEnabled(PREF_DAILY_SUMMARY)) return

        val notification = NotificationCompat.Builder(context, CHANNEL_ID_SUMMARY)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .build()

        NotificationManagerCompat.from(context).notify(1003, notification)
    }

    suspend fun sendWeeklyReport(title: String, content: String) {
        if (!isNotificationTypeEnabled(PREF_WEEKLY_REPORT)) return

        val notification = NotificationCompat.Builder(context, CHANNEL_ID_REPORT)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .build()

        NotificationManagerCompat.from(context).notify(1004, notification)
    }

    private suspend fun isNotificationTypeEnabled(prefKey: String): Boolean {
        val settings = getNotificationSettings()
        if (!settings.notificationsEnabled) return false

        return when (prefKey) {
            PREF_GROWTH_REMINDERS -> settings.growthReminders
            PREF_ANALYSIS_NOTIFICATIONS -> settings.analysisNotifications
            PREF_DAILY_SUMMARY -> settings.dailySummary
            PREF_WEEKLY_REPORT -> settings.weeklyReport
            else -> false
        }
    }
}
