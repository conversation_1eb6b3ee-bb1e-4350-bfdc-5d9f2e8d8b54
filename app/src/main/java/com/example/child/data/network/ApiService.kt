package com.example.child.data.network

import com.example.child.data.network.dto.*
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    
    // 用户认证
    @POST("auth/send-sms")
    suspend fun sendSmsCode(@Body request: SendSmsRequest): Response<ApiResponse<Unit>>
    
    @POST("auth/verify-sms")
    suspend fun verifySmsCode(@Body request: VerifySmsRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("auth/logout")
    suspend fun logout(@Header("Authorization") token: String): Response<ApiResponse<Unit>>
    
    // 用户信息
    @GET("user/profile")
    suspend fun getUserProfile(@Header("Authorization") token: String): Response<ApiResponse<UserProfileResponse>>
    
    @PUT("user/profile")
    suspend fun updateUserProfile(
        @Header("Authorization") token: String,
        @Body request: UpdateProfileRequest
    ): Response<ApiResponse<UserProfileResponse>>
    
    @PUT("user/password")
    suspend fun changePassword(
        @Header("Authorization") token: String,
        @Body request: ChangePasswordRequest
    ): Response<ApiResponse<Unit>>
    
    // 音频上传和处理
    @Multipart
    @POST("audio/upload")
    suspend fun uploadAudio(
        @Header("Authorization") token: String,
        @Part file: MultipartBody.Part,
        @Part("date") date: String,
        @Part("hour") hour: Int
    ): Response<ApiResponse<UploadAudioResponse>>
    
    @POST("audio/process")
    suspend fun processAudio(
        @Header("Authorization") token: String,
        @Body request: ProcessAudioRequest
    ): Response<ApiResponse<ProcessAudioResponse>>
    
    // 分析结果
    @GET("analysis/daily/{date}")
    suspend fun getDailyAnalysis(
        @Header("Authorization") token: String,
        @Path("date") date: String
    ): Response<ApiResponse<DailyAnalysisResponse>>
    
    @GET("analysis/monthly/{year}/{month}")
    suspend fun getMonthlyReport(
        @Header("Authorization") token: String,
        @Path("year") year: Int,
        @Path("month") month: Int
    ): Response<ApiResponse<MonthlyReportResponse>>
    
    // AI 问答
    @POST("ai/chat")
    suspend fun chatWithAI(
        @Header("Authorization") token: String,
        @Body request: ChatRequest
    ): Response<ApiResponse<ChatResponse>>
}
