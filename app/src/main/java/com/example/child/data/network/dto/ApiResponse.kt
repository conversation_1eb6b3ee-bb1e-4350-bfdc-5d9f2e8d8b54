package com.example.child.data.network.dto

data class ApiResponse<T>(
    val success: Boolean,
    val message: String?,
    val data: T?,
    val code: Int?
)

// 认证相关
data class SendSmsRequest(
    val phone: String
)

data class VerifySmsRequest(
    val phone: String,
    val code: String
)

data class LoginResponse(
    val token: String,
    val user: UserProfileResponse,
    val isNewUser: Boolean
)

data class RegisterRequest(
    val phone: String,
    val name: String,
    val childName: String,
    val childAge: Int,
    val childGender: String
)

// 用户相关
data class UserProfileResponse(
    val id: String,
    val phone: String,
    val name: String?,
    val avatar: String?,
    val childName: String?,
    val childAge: Int?,
    val childGender: String?,
    val createdAt: Long,
    val updatedAt: Long
)

data class UpdateProfileRequest(
    val name: String?,
    val childName: String?,
    val childAge: Int?,
    val childGender: String?
)

data class ChangePasswordRequest(
    val oldPassword: String,
    val newPassword: String
)

// 音频相关
data class UploadAudioResponse(
    val audioId: String,
    val filePath: String
)

data class ProcessAudioRequest(
    val audioId: String
)

data class ProcessAudioResponse(
    val audioId: String,
    val transcription: String,
    val analysis: String
)

// 分析相关
data class DailyAnalysisResponse(
    val id: String,
    val date: String,
    val overallMood: String?,
    val learningProgress: String?,
    val behaviorAnalysis: String?,
    val growthHighlights: String?,
    val suggestions: String?,
    val happinessLevel: Int?,
    val activityLevel: Int?,
    val socialInteraction: String?,
    val audioRecords: List<AudioRecordResponse>
)

data class AudioRecordResponse(
    val id: String,
    val fileName: String,
    val duration: Long,
    val recordHour: Int,
    val transcription: String?,
    val analysis: String?
)

data class MonthlyReportResponse(
    val id: String,
    val year: Int,
    val month: Int,
    val summary: String?,
    val keyMilestones: List<String>,
    val averageHappiness: Float?,
    val averageActivity: Float?,
    val totalAudioHours: Float?,
    val growthAreas: List<String>,
    val recommendations: List<String>,
    val dailyAnalysis: List<DailyAnalysisResponse>
)

// AI 聊天相关
data class ChatRequest(
    val message: String,
    val context: String? = null
)

data class ChatResponse(
    val reply: String,
    val timestamp: Long
)
