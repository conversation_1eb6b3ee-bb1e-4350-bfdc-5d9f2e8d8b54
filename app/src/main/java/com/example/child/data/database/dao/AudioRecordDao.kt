package com.example.child.data.database.dao

import androidx.room.*
import com.example.child.data.model.AudioRecord
import kotlinx.coroutines.flow.Flow

@Dao
interface AudioRecordDao {
    
    @Query("SELECT * FROM audio_records WHERE userId = :userId ORDER BY createdAt DESC")
    fun getAudioRecordsByUser(userId: String): Flow<List<AudioRecord>>
    
    @Query("SELECT * FROM audio_records WHERE userId = :userId AND recordDate = :date ORDER BY recordHour ASC")
    suspend fun getAudioRecordsByDate(userId: String, date: String): List<AudioRecord>
    
    @Query("SELECT * FROM audio_records WHERE userId = :userId AND recordDate = :date AND recordHour = :hour")
    suspend fun getAudioRecordByHour(userId: String, date: String, hour: Int): AudioRecord?
    
    @Query("SELECT * FROM audio_records WHERE id = :id")
    suspend fun getAudioRecordById(id: String): AudioRecord?
    
    @Query("SELECT * FROM audio_records WHERE isProcessed = 0")
    suspend fun getUnprocessedRecords(): List<AudioRecord>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAudioRecord(audioRecord: AudioRecord)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAudioRecords(audioRecords: List<AudioRecord>)
    
    @Update
    suspend fun updateAudioRecord(audioRecord: AudioRecord)
    
    @Delete
    suspend fun deleteAudioRecord(audioRecord: AudioRecord)
    
    @Query("DELETE FROM audio_records WHERE userId = :userId")
    suspend fun deleteAudioRecordsByUser(userId: String)
}
