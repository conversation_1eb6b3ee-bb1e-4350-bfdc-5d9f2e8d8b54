<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 默认主题颜色 - 兼容性 -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>

    <!-- 主色调 -->
    <color name="primary">#FF6B73FF</color>
    <color name="primary_variant">#FF5A63E8</color>
    <color name="secondary">#FFFF9F43</color>
    <color name="secondary_variant">#FFFF8C2A</color>

    <!-- 背景色 -->
    <color name="background">#FFF8F9FA</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="surface_variant">#FFF5F5F5</color>

    <!-- 文字颜色 -->
    <color name="on_primary">#FFFFFFFF</color>
    <color name="on_secondary">#FFFFFFFF</color>
    <color name="on_background">#FF1A1A1A</color>
    <color name="on_surface">#FF1A1A1A</color>
    <color name="text_primary">#FF1A1A1A</color>
    <color name="text_secondary">#FF666666</color>
    <color name="text_hint">#FF999999</color>

    <!-- 状态颜色 -->
    <color name="success">#FF4CAF50</color>
    <color name="warning">#FFFF9800</color>
    <color name="error">#FFF44336</color>
    <color name="info">#FF2196F3</color>

    <!-- 通用颜色 -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="transparent">#00000000</color>
    <color name="divider">#FFE0E0E0</color>
    <color name="shadow">#1A000000</color>

    <!-- 渐变色 -->
    <color name="gradient_start">#FF6B73FF</color>
    <color name="gradient_end">#FF9C27B0</color>
</resources>