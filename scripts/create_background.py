#!/usr/bin/env python3
"""
创建一个简单的背景图片
需要安装 Pillow: pip install Pillow
"""

from PIL import Image, ImageDraw
import os

def create_gradient_background():
    # 创建图片尺寸 (1080x1920 - 常见的手机屏幕比例)
    width, height = 1080, 1920

    # 创建新图片
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)

    # 创建更明显的渐变效果（从紫色到蓝色到青色）
    for y in range(height):
        # 计算渐变比例
        ratio = y / height

        # 定义颜色 (从紫色到蓝色到青色，更明显的颜色)
        if ratio < 0.33:
            # 上部分：从紫色到蓝色
            r = int(138 + (74 - 138) * (ratio * 3))      # 8A2BE2 -> 4A90E2
            g = int(43 + (144 - 43) * (ratio * 3))
            b = int(226 + (226 - 226) * (ratio * 3))
        elif ratio < 0.66:
            # 中部分：从蓝色到青色
            r = int(74 + (78 - 74) * ((ratio - 0.33) * 3))   # 4A90E2 -> 4ECDC4
            g = int(144 + (205 - 144) * ((ratio - 0.33) * 3))
            b = int(226 + (196 - 226) * ((ratio - 0.33) * 3))
        else:
            # 下部分：从青色到浅青色
            r = int(78 + (200 - 78) * ((ratio - 0.66) * 3))   # 4ECDC4 -> C8F7F5
            g = int(205 + (247 - 205) * ((ratio - 0.66) * 3))
            b = int(196 + (245 - 196) * ((ratio - 0.66) * 3))

        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)

    # 添加一些装饰元素来确保图片可见
    # 在四个角落添加小圆圈
    circle_radius = 50
    draw.ellipse([10, 10, 10 + circle_radius, 10 + circle_radius], fill=(255, 255, 255))
    draw.ellipse([width - 60, 10, width - 10, 60], fill=(255, 255, 255))
    draw.ellipse([10, height - 60, 60, height - 10], fill=(255, 255, 255))
    draw.ellipse([width - 60, height - 60, width - 10, height - 10], fill=(255, 255, 255))

    return image

def main():
    # 创建输出目录
    output_dir = "../app/src/main/assets/images/backgrounds"
    os.makedirs(output_dir, exist_ok=True)

    # 创建背景图片
    background = create_gradient_background()

    # 保存图片
    output_path = os.path.join(output_dir, "background.png")
    background.save(output_path, "PNG", optimize=True)

    print(f"背景图片已创建: {output_path}")
    print(f"图片尺寸: {background.size}")

if __name__ == "__main__":
    main()
